## 0.17.2 (2025-05-12)
### Bugfix
* Rollback abstract [(1b91043)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/1b910438300b00ededde92bac5ea2fb3aa683e70)

## 0.17.1 (2025-05-12)
### Bugfix
* Correction_gestion_des_etats [(e5edfb9)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e5edfb99c406a4f0e02aa4241018ced13b6418bb)

## 0.17.0 (2025-05-12)
### Features
* GestionErreursLoader [(8583bcf)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/8583bcf6894c4cf6ea9f9d5e3a387dc086aa60f4)
* Sonar-qube [(fac39d2)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fac39d21df2ac911932bb3a8f520b0e323cd61c5)

### Bugfix
* InclusionToBeContinuous [(832b43d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/832b43d475b1763de24ccd2fc47d1356f6eb7f6b)
* AmeliorationGestionErreurs [(f902771)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/f9027716686261a66ad0a0965dc32aa69d341233)
* Amelioration loggging appel  embedding [(4328aaa)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4328aaaef63f7883b21abeefd8a74f56178e88e1)

## 0.16.0 (2025-04-17)
### Features
* Remove_verification_prod [(62be39b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/62be39b2f7c9b1880e10195d9a10621753619783)
* DeclarationAutoDesChanges [(0613c48)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0613c48d4aba9c348954491c3409fde4ccaf3c8c)
* Schedule load for sandbox [(4f9f93b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4f9f93b888cfb7cb2857a957f093494f8cbfe89f)
* Sharepoint by perimeter and test new sharepoint config [(d9b2077)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d9b2077723c7031026512ba39b740e0737a74c7e)

### Bugfix
* Rollback sur l'envoi d'exceptions [(55e1642)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/55e1642bc4851eb60751dd50272564ef19d19ae6)
* KBOT-274-DocumentsNonSupprimes [(b351e6f)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/b351e6fbff7bb2aed88792ffd489386d0459a509)
* Env GCP_PROJECT replaced by GCP_PROJECT_ID [(dd8654c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/dd8654c99dc978ba743051550c01a4a6ebc05ac2)
* No secret manager service client for tests as we have no credentials on git [(0d56a37)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0d56a37e27a03e9637588a15648e6cc6b4614a5b)
* CorrectionGetSecret [(5f0fd6e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/5f0fd6e855a37d9b5bbe0298b5698697784aab9a)
* Correction gitlab-ci [(366d49c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/366d49cb2abe9e93cfb929514bf73bdb4cb0b009)
* MiseEnCommentaireCreationChangeEnAttendantCreationCompte [(651cd0e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/651cd0ef3a1e4117bb91935b2aca79560534b93d)
* MiseEnCommentaireCreationChangeEnAttendantCreationCompte [(347ddbe)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/347ddbef0e655d78cced997515f6d25df00b87f0)

## 0.15.0 (2025-03-31)
### Features
* DeclarationAutoDesMeps [(ff16757)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/ff167576a49b1ba97c29eaff899898635aeacf61)
* DeclarationAutoDesMeps [(fa3da58)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fa3da5850608362b1909368a0dc02dd21f459c20)
* DeclarationAutoDesMeps [(fac86e3)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fac86e38c91a4a7d44ca5a5ef3123eb686f8e881)

## 0.14.0 (2025-03-27)
### Features
* Kbot-243-GestionRepriseApresErreur [(61429c9)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/61429c9090f7d0997fa6b55d4daefb880e1ae03c)

## 0.13.0 (2025-03-21)
### Features
* Kbot-243-GestionRepriseApresErreur2 [(d1803d2)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d1803d2a830cb3dee21e97cfafdfe5e4010384de)

### Bugfix
* CorrectionBugAlerting [(7f25f3c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/7f25f3ca09398cb73ce44166ea44238e9404f1ba)

## 0.12.1 (2025-03-20)
### Bugfix
* When string_date is empty return today's date [(46216ba)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/46216ba22604d8645a3877688fd872d99e8749e2)
* Put privateModification in modificationDate and put modificationDate in publicModificationDate [(fc67389)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fc673891f775a7f5e6fdc122f6873b74a65c54d2)

## 0.12.0 (2025-03-20)
### Features
* Gatape preprod [(833c617)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/833c617e7d09d32ecd929dee44e02c7afd6dec41)
* Add ca_certificate [(bce5b15)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/bce5b15eaffd15f69fa001931bfdbad151e89a3d)
* Gatape [(e1dc6ce)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e1dc6ce2ac60702c9f329ba1c28889632821e2f7)

### Bugfix
* CheckMetadataRemovingSomeCheckings [(36d7a8f)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/36d7a8f3c52fc3b3127c49258ee5e565a68689ba)
* Tidy all what I can [(806f62d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/806f62d8d27ab8ea21e0ad2b7cf0221909fabe14)
* Add variable ROOT_TESTS for tests in gitlab-ci [(528febf)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/528febf5986ad875cfc4fda61560b48532ec63cf)
* Adaptations for tests and for prod [(d0294c3)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d0294c3e58f39a83ac47ae62a3e900f61138c4cd)
* Remove debug print [(c6de342)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/c6de34208fc887265fed8732b61dda3ba054e905)
* Verify certificate also when calling api [(437424f)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/437424f177373d9402235a118d2faf51a493c17d)
* Call to backend via gatape must use proxy [(0ee982a)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0ee982aba3286f3b013d3cedf7bb87745ef14e6e)

## 0.11.0 (2025-03-18)
### Features
* Url content fix preprocess [(3605b6a)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/3605b6a0388cd43c7151145934a10ca53015eaf5)

## 0.10.0 (2025-03-14)
### Features

## 0.9.0 (2025-03-13)
### Features
* RemonteExceptionTestMauvaiseUrlSharepoint [(70e521e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/70e521ebf5a811bbd570e1135498a562ef8f16ca)
* Adding critical messages to send probes to supervision [(3e1b604)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/3e1b6043f7a777a024ff455df1dc1f0ff7489361)
* ModifSuiteRevueCode [(0e6c979)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0e6c97999afad1dead9ecb1795b91664a658d715)
* KBOT-201-Embedding-full [(d62e75a)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d62e75a60f6ec2f1cc695a65400fa3873c4ceeec)

### Bugfix
* GoodUrlForSharepoint [(1c50edd)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/1c50edde033072ed2f24daa8b5a5122ce2d3ac1e)
* /gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/33dc4a2fd7a67f53a41420b645f89395d2b91933)
* /gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/59f0e08e6785223e6112c48213bbd6882d25dba9)

## 0.8.0 (2025-03-04)
### Features
* NonLaunchInHO [(0c822bf)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0c822bfd28cb14d941a49de8073c702f353470ad)

## 0.7.0 (2025-02-27)
### Features
* ChangingFieldsChecking [(74e6b19)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/74e6b19c8e38c59fdf3e0d56e79c4dd7ef78ff94)
* ChangingFieldsChecking [(ff4f9ec)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/ff4f9ec5d11b38d673bbd9d8016653f3c919119a)

## 0.6.0 (2025-02-25)
### Features
* Adding perimeter code in all logs [(8d2b1d8)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/8d2b1d873f0063c993dcc019e7bd143cce625444)
* Ajout de logs pertinents [(3f0c3d3)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/3f0c3d318c5c3221f2113c57a349f5db1338deec)

## 0.5.0 (2025-02-17)
### Features
* AddingFieldsCheck and converting date  to UTC for basic sheets [(f73068c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/f73068c3703f3e790adcf60b5a8fb79c849a3361)

### Bugfix
* ErreurCheckTabs and Cases [(dc0339d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/dc0339d09f2d55658cca237818c6c369f4b7c9f2)

## 0.4.0 (2025-01-30)
### Features
* ScheduleOnPreprod [(cdc0d97)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/cdc0d971f5ed3843d5295041fb22cef7549321c8)

## 0.3.0 (2025-01-30)
### Features
* Addingscheduler to prod [(88fda3c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/88fda3c7457fd81cb8713e623fa4f61ed1bb8155)

## 0.2.1 (2025-01-24)
### Bugfix
* RollbackPrecedenteModif [(69f3913)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/69f39137adce8291728a527ba9327b1cb88593b7)
* ChangingSideCarImagePath [(72ddce7)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/72ddce7dbc6adf491737c91d92d20acd7aa2a925)

## 0.2.0 (2025-01-14)
### Features
* GestionDesGetUnitaires + Purge [(ebd2626)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/ebd262600fcfbd04c7947b24bd4a66dcc4e91aba)

## 0.1.0 (2025-01-13)
### Features
* DeployschedulerOnlyProdOrDev [(9d14e4f)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/9d14e4f8e496ad6f0293d022f0788acde2037285)
* RecuperationDesIds Adding scheduler for ebotman [(6c49053)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/6c490535a5c2a9837ad77ea610a7435a03826ac0)
* Tests-sharepoint Adding tests on sharepoint [(58862af)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/58862af7e1e20ba3fbcab80872439a4ceccdcfa0)
* Embed documents [(78e96f0)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/78e96f0fecf94b736214c636b7e1e5f1232df58a)
* Add tests on compare_list [(3ed7b77)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/3ed7b77e6943c3e2cc1ffb7f96d3d12a90e67346)
* Compare list [(a5e09b3)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/a5e09b36352e6837ae82bda10d9ee87b9c368375)
* Set source done after writing getlist [(411952b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/411952bce3354cab88ac7d674bb2e34433ddbc9a)
* Get documents with gcs loader [(4674c8c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4674c8c819af76c0c5c5bcc532c621d260eabf15)
* Get document list with GCS loader [(0d3f313)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0d3f3137bb8d8b4b917cd384396693a821532361)
* Add sources load [(a556095)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/a556095c4ad7bc8f4318117a234716c6333bb91c)

### Bugfix
* NomBucketPreprod [(4bb5be2)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4bb5be2ccd119fc506bb1b1c6799ec142e9621ac)
* Gitlabci-env [(e442f7d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e442f7d6a156c189e40907e5305211e691b2dfc3)
* /gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/72f424badce27a69e0eeff7231e4f51165370498)
* Suppression code inutile [(2284580)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/2284580ac06d7cc76185fbd437a889c6cf718901)
* AddingDomainAndSourceInID [(d1a18d4)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d1a18d43349acd3537e7fa5dd4d5e01594875f93)
* BugSurCompare [(e5a6da0)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e5a6da02b057e4880122546c49e621e1c83d7f60)
* Improving match_date_value [(4f1e04e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4f1e04efc625a1192f234309a79452afaeb2723b)
* ExtensionFichesBasic [(426eb66)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/426eb66b6b6893830c719534d9022664b3eb04c9)
* Test [(a9b1b34)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/a9b1b34edf61d40361d3e1ba00f8c47aa5a2c0f3)
* GestionDesErreurs [(2718c2d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/2718c2d02c358e7318da52bb7c7c190749ed5de4)
* Improving logs [(d3dd4cb)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d3dd4cb74742e63f62320a6a590f02357d553290)
* Excluding files in stat in_progress or done [(ccfafb8)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/ccfafb88dd980981e841e32c441773ec935bba7a)
* Adding logs [(d621994)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/d621994aa4f2bd9dd569df68ce27d4eee3603bae)
* Priorizing treatments [(9364c0e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/9364c0e612dd4877ee9d73a990985941b5b2cd9e)
* /gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/8badc447864349d7ad25c3dc6a758b54ae49445c)
* Correct-debug-message and add env and version in docs [(11d8953)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/11d89531c81264c5ddd146a676fdc4140123d33e)
* Problem with modificationDate [(84192d5)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/84192d51e5030bbb9bc29a07f805a0e85f19d830)
* Iam-bindings [(fa6d27e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fa6d27ec881166e5378ec307a1ff13bc86bb67b8)
* Sidecar [(62458b2)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/62458b2a355b76a740d498b62479373ba5048b13)
* Missing / after docs for location [(6b7c114)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/6b7c1149ffee2ce68ad03e85daad0c9d9f3a68a7)
* Deal with case modif_date is string or datetime [(fa7a3f5)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fa7a3f5769584b55b57bb75833c764b312144883)
* Amelioration tidy pour ne pas avoir de sous repertoires [(5395535)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/53955358ff20e7fa0e7a552a2460944c659bdedd)
* Embedding return a datetime [(2622015)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/26220153178ea6506efaf5605c6c5796a8b2e804)
* Add + and * [(e7d5aa4)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e7d5aa4445a4df773c87d7273f517007c00930ec)
* Tidy file name from sharepoint when writing file to gcs [(5a44e3d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/5a44e3de8a181e384e15cba9086f236134a45c52)
* Connect to other api [(81544d0)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/81544d02d937ea7be7ec9dbba612fbf5089a0b1c)
* Ortho [(300678b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/300678b41bb6d5c706bc22c266a9fc02f259255d)

## 0.0.1 (2024-11-20)
### Bugfix
* Correct-debug-message and add env and version in docs [(11d8953)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/11d89531c81264c5ddd146a676fdc4140123d33e)
* Problem with modificationDate [(84192d5)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/84192d51e5030bbb9bc29a07f805a0e85f19d830)

## 0.0.0 (2024-11-15)
### Features
* Embed documents [(78e96f0)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/78e96f0fecf94b736214c636b7e1e5f1232df58a)
* Add tests on compare_list [(3ed7b77)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/3ed7b77e6943c3e2cc1ffb7f96d3d12a90e67346)
* Compare list [(a5e09b3)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/a5e09b36352e6837ae82bda10d9ee87b9c368375)
* Set source done after writing getlist [(411952b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/411952bce3354cab88ac7d674bb2e34433ddbc9a)
* Get documents with gcs loader [(4674c8c)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/4674c8c819af76c0c5c5bcc532c621d260eabf15)
* Get document list with GCS loader [(0d3f313)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/0d3f3137bb8d8b4b917cd384396693a821532361)
* Add sources load [(a556095)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/a556095c4ad7bc8f4318117a234716c6333bb91c)

### Bugfix
* Iam-bindings [(fa6d27e)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fa6d27ec881166e5378ec307a1ff13bc86bb67b8)
* Sidecar [(62458b2)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/62458b2a355b76a740d498b62479373ba5048b13)
* Missing / after docs for location [(6b7c114)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/6b7c1149ffee2ce68ad03e85daad0c9d9f3a68a7)
* Deal with case modif_date is string or datetime [(fa7a3f5)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/fa7a3f5769584b55b57bb75833c764b312144883)
* Amelioration tidy pour ne pas avoir de sous repertoires [(5395535)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/53955358ff20e7fa0e7a552a2460944c659bdedd)
* Embedding return a datetime [(2622015)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/26220153178ea6506efaf5605c6c5796a8b2e804)
* Add + and * [(e7d5aa4)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/e7d5aa4445a4df773c87d7273f517007c00930ec)
* Tidy file name from sharepoint when writing file to gcs [(5a44e3d)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/5a44e3de8a181e384e15cba9086f236134a45c52)
* Connect to other api [(81544d0)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/81544d02d937ea7be7ec9dbba612fbf5089a0b1c)
* Ortho [(300678b)](https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler/commits/300678b41bb6d5c706bc22c266a9fc02f259255d)
