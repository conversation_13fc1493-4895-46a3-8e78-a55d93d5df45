# 🔐 Système de Secrets pour les Tests

Ce répertoire contient les secrets de test pour simuler Google Cloud Secret Manager en local.

## 🎯 Objectif

- **Tests locaux** : <PERSON><PERSON><PERSON> les appels réels à Google Cloud Secret Manager
- **Développement** : Avoir des credentials de test disponibles
- **CI/CD** : Exécuter les tests sans accès GCP

## 📁 Structure

```
conf/etc/secrets/tests/
├── basic-client-id/secret                    # Client ID pour BasicLoader
├── basic-client-secret/secret                # Client Secret pour BasicLoader
├── sharepoint-client-config/secret           # Config SharePoint
├── sharepoint-client-private-key/secret      # Clé privée SharePoint
├── confluence-credentials/secret             # Credentials Confluence globaux
└── {perimeter}-confluence-credentials/secret # Credentials par périmètre
```

## 🔧 Comment ça fonctionne

Le `ConfigWithSecret` utilise cette logique :

1. **D'abord** : Cherche dans `conf/etc/secrets/tests/{secret_id}/secret`
2. **Ensuite** : Si pas trouvé, utilise Google Cloud Secret Manager

## 📝 Configuration Confluence

### Format recommandé (JSON)

```json
{
  "pat_token": "votre_personal_access_token"
}
```

### Clés de secrets supportées

| Clé | Description | Priorité |
|-----|-------------|----------|
| `{perimeter_code}-confluence-credentials` | Credentials spécifiques au périmètre | 1 (haute) |
| `confluence-credentials` | Credentials globaux | 2 (moyenne) |
| `{perimeter_code}-confluence-pat-token` | PAT token legacy | 3 (basse) |

### Exemples de clés

- `test-perimeter-confluence-credentials`
- `main-confluence-credentials`
- `confluence-credentials`

## 🚀 Configuration pour vos tests

### 1. Créer le répertoire

```bash
mkdir -p conf/etc/secrets/tests/confluence-credentials
```

### 2. Créer le fichier secret

```bash
echo '{"pat_token": "VOTRE_VRAI_TOKEN"}' > conf/etc/secrets/tests/confluence-credentials/secret
```

### 3. Pour un périmètre spécifique

```bash
mkdir -p conf/etc/secrets/tests/mon-perimeter-confluence-credentials
echo '{"pat_token": "TOKEN_SPECIFIQUE"}' > conf/etc/secrets/tests/mon-perimeter-confluence-credentials/secret
```

## 🔒 Sécurité

⚠️ **IMPORTANT** : 
- Ces fichiers contiennent de vrais secrets pour les tests
- Ils sont dans `.gitignore` et ne doivent PAS être committés
- Utilisez des tokens de test avec permissions limitées
- Créez un espace Confluence dédié aux tests

## 🧪 Utilisation dans les tests

### Tests unitaires (avec mocks)
```python
# Les tests unitaires utilisent des mocks, pas ces secrets
config_with_secret.get_confluence_credentials = MagicMock(return_value={
    "pat_token": "fake_token"
})
```

### Tests d'intégration (avec vrais secrets)
```python
# Les tests d'intégration utilisent ces secrets automatiquement
# si env != 'tests' dans ConfigWithSecret
```

## 📋 Checklist de configuration

- [ ] Créer `confluence-credentials/secret` avec votre PAT token
- [ ] Vérifier que le token a les permissions de lecture
- [ ] Créer un espace Confluence de test
- [ ] Tester avec `python run_integration_tests.py --check-config`

## 🔄 Migration vers GCP Secret Manager

En production, ces secrets doivent être dans Google Cloud Secret Manager :

```bash
# Créer le secret dans GCP
gcloud secrets create confluence-credentials --data-file=secret.json

# Format du secret.json
{
  "pat_token": "votre_token_production"
}
```
