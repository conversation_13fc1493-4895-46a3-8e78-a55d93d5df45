# Scripts Utilitaires - kbot-load-scheduler

Ce répertoire contient des scripts utilitaires pour le développement et la configuration du projet kbot-load-scheduler.

## 📁 Scripts Disponibles

### 🔐 Configuration des Secrets

#### `setup_confluence_secrets.py`
Script pour configurer facilement les secrets Confluence pour les tests et le développement.

**Fonctionnalités :**
- Configuration interactive des secrets
- Import depuis variables d'environnement
- Vérification des secrets existants
- Support des formats JSON et legacy

**Usage :**

```bash
# Configuration interactive (recommandé)
python scripts/setup_confluence_secrets.py --interactive

# Configuration depuis variables d'environnement
python scripts/setup_confluence_secrets.py --from-env

# Configuration avec un token spécifique
python scripts/setup_confluence_secrets.py --token YOUR_PAT_TOKEN

# Lister les secrets existants
python scripts/setup_confluence_secrets.py --list

# Vérifier la configuration
python scripts/setup_confluence_secrets.py --verify

# Aide
python scripts/setup_confluence_secrets.py --help
```

**Prérequis :**
- Variable d'environnement `CONFLUENCE_PAT_TOKEN` (pour `--from-env`)
- Ou fichier `.env` dans `tests/loader/confluence/`

**Secrets créés :**
- `confluence-credentials` (global)
- `test-perimeter-confluence-credentials` (pour les tests)

## 🔧 Développement

### Ajout de Nouveaux Scripts

Lors de l'ajout de nouveaux scripts utilitaires :

1. **Placez le script** dans ce répertoire `scripts/`
2. **Ajoutez la documentation** dans ce README
3. **Utilisez des chemins relatifs** depuis la racine du projet
4. **Incluez l'aide** (`--help`) dans vos scripts
5. **Testez** depuis la racine du projet

### Conventions

- **Nommage** : `action_module.py` (ex: `setup_confluence_secrets.py`)
- **Shebang** : `#!/usr/bin/env python3`
- **Encoding** : `# -*- coding: utf-8 -*-`
- **Documentation** : Docstring avec exemples d'usage
- **Arguments** : Utiliser `argparse` pour les options
- **Chemins** : Relatifs à la racine du projet

### Exemple de Structure

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Description du script.

Usage:
    python scripts/mon_script.py --help
    python scripts/mon_script.py --option
"""

import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Description")
    # ... arguments
    args = parser.parse_args()
    # ... logique

if __name__ == "__main__":
    main()
```

## 🚀 Utilisation

### Depuis la Racine du Projet

Tous les scripts doivent être exécutés depuis la racine du projet :

```bash
# Correct
cd /path/to/kbot-load-scheduler
python scripts/setup_confluence_secrets.py --interactive

# Incorrect
cd scripts
python setup_confluence_secrets.py --interactive
```

### Variables d'Environnement

Certains scripts peuvent nécessiter des variables d'environnement :

```bash
# Exemple pour Confluence
export CONFLUENCE_URL=https://mycompany.atlassian.net
export CONFLUENCE_PAT_TOKEN=your_token_here

python scripts/setup_confluence_secrets.py --from-env
```

## 📝 Contribution

Pour contribuer de nouveaux scripts :

1. **Suivez les conventions** de nommage et structure
2. **Documentez** le script dans ce README
3. **Testez** depuis la racine du projet
4. **Incluez des exemples** d'utilisation
5. **Gérez les erreurs** de manière appropriée

## 🔗 Liens Utiles

- **[Configuration Confluence](../src/kbotloadscheduler/loader/confluence/docs/INTEGRATION_README.md)** - Guide d'intégration
- **[Tests Confluence](../tests/loader/confluence/README.md)** - Documentation des tests
- **[Documentation Principale](../README.md)** - Documentation du projet
