#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour configurer facilement les secrets Confluence pour les tests.

Usage:
    python setup_confluence_secrets.py --help
    python setup_confluence_secrets.py --interactive
    python setup_confluence_secrets.py --token YOUR_TOKEN
    python setup_confluence_secrets.py --from-env
"""

import os
import json
import argparse
from pathlib import Path
from dotenv import load_dotenv


def create_secret_file(secret_name: str, content: dict):
    """Crée un fichier de secret avec le contenu donné."""
    # Chemin vers le répertoire des secrets (relatif au script)
    secrets_base_dir = Path(__file__).parent.parent / "conf" / "etc" / "secrets" / "tests"
    secret_dir = secrets_base_dir / secret_name
    secret_file = secret_dir / "secret"
    
    # Créer le répertoire
    secret_dir.mkdir(parents=True, exist_ok=True)
    
    # Écrire le contenu
    with open(secret_file, 'w') as f:
        json.dump(content, f, indent=2)
    
    print(f"✅ Secret créé: {secret_file}")
    return secret_file


def setup_interactive():
    """Configuration interactive des secrets."""
    print("🔧 Configuration interactive des secrets Confluence")
    print("=" * 50)
    
    # Demander le PAT token
    pat_token = input("Entrez votre Personal Access Token Confluence: ").strip()
    if not pat_token:
        print("❌ PAT token requis")
        return False
    
    # Créer le secret global
    global_content = {"pat_token": pat_token}
    create_secret_file("confluence-credentials", global_content)
    
    # Demander s'il faut créer des secrets par périmètre
    create_perimeter = input("Créer des secrets par périmètre ? (o/N): ").lower().strip()
    if create_perimeter in ['o', 'oui', 'y', 'yes']:
        perimeter = input("Nom du périmètre (ex: test-perimeter): ").strip()
        if perimeter:
            perimeter_content = {"pat_token": pat_token}
            create_secret_file(f"{perimeter}-confluence-credentials", perimeter_content)
    
    print("\n✅ Configuration terminée !")
    return True


def setup_from_token(token: str):
    """Configure les secrets à partir d'un token donné."""
    print(f"🔧 Configuration avec token: {token[:10]}...")
    
    # Secret global
    global_content = {"pat_token": token}
    create_secret_file("confluence-credentials", global_content)
    
    # Secret pour test-perimeter (utilisé dans les tests)
    test_content = {"pat_token": token}
    create_secret_file("test-perimeter-confluence-credentials", test_content)
    
    print("✅ Secrets configurés pour les tests")
    return True


def setup_from_env():
    """Configure les secrets à partir des variables d'environnement."""
    print("🔧 Configuration depuis les variables d'environnement...")
    
    # Charger .env depuis le répertoire des tests d'intégration
    test_env_file = Path(__file__).parent.parent / "tests" / "loader" / "confluence" / ".env"
    if test_env_file.exists():
        load_dotenv(test_env_file)
        print(f"📁 Chargé: {test_env_file}")
    else:
        load_dotenv()
        print("📁 Chargé depuis l'environnement système")
    
    # Récupérer le token
    pat_token = os.getenv("CONFLUENCE_PAT_TOKEN")
    if not pat_token:
        print("❌ Variable CONFLUENCE_PAT_TOKEN non trouvée")
        print("💡 Créez un fichier .env dans tests/loader/confluence/")
        return False
    
    return setup_from_token(pat_token)


def list_secrets():
    """Liste les secrets existants."""
    print("📋 Secrets Confluence existants:")
    print("=" * 40)
    
    secrets_dir = Path(__file__).parent.parent / "conf" / "etc" / "secrets" / "tests"
    confluence_secrets = []
    
    if secrets_dir.exists():
        for item in secrets_dir.iterdir():
            if item.is_dir() and "confluence" in item.name:
                secret_file = item / "secret"
                if secret_file.exists():
                    try:
                        with open(secret_file) as f:
                            content = json.load(f)
                        
                        token = content.get("pat_token", "")
                        token_preview = f"{token[:10]}..." if token else "❌ Pas de token"
                        
                        confluence_secrets.append((item.name, token_preview))
                    except Exception as e:
                        confluence_secrets.append((item.name, f"❌ Erreur: {e}"))
    
    if confluence_secrets:
        for name, token in confluence_secrets:
            print(f"  ✅ {name}: {token}")
    else:
        print("  ❌ Aucun secret Confluence trouvé")
    
    return len(confluence_secrets) > 0


def verify_secrets():
    """Vérifie que les secrets sont correctement configurés."""
    print("🔍 Vérification des secrets...")
    
    required_secrets = [
        "confluence-credentials",
        "test-perimeter-confluence-credentials"
    ]
    
    all_good = True
    secrets_base_dir = Path(__file__).parent.parent / "conf" / "etc" / "secrets" / "tests"
    
    for secret_name in required_secrets:
        secret_file = secrets_base_dir / secret_name / "secret"
        
        if secret_file.exists():
            try:
                with open(secret_file) as f:
                    content = json.load(f)
                
                if content.get("pat_token"):
                    print(f"  ✅ {secret_name}: OK")
                else:
                    print(f"  ❌ {secret_name}: Pas de pat_token")
                    all_good = False
            except Exception as e:
                print(f"  ❌ {secret_name}: Erreur - {e}")
                all_good = False
        else:
            print(f"  ❌ {secret_name}: Fichier manquant")
            all_good = False
    
    if all_good:
        print("\n✅ Tous les secrets sont correctement configurés")
    else:
        print("\n❌ Certains secrets nécessitent une configuration")
    
    return all_good


def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(
        description="Configuration des secrets Confluence pour les tests"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Configuration interactive"
    )
    
    parser.add_argument(
        "--token", "-t",
        type=str,
        help="Configurer avec un token spécifique"
    )
    
    parser.add_argument(
        "--from-env", "-e",
        action="store_true",
        help="Configurer depuis les variables d'environnement"
    )
    
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="Lister les secrets existants"
    )
    
    parser.add_argument(
        "--verify", "-v",
        action="store_true",
        help="Vérifier les secrets"
    )
    
    args = parser.parse_args()
    
    if args.list:
        list_secrets()
    elif args.verify:
        verify_secrets()
    elif args.interactive:
        setup_interactive()
    elif args.token:
        setup_from_token(args.token)
    elif args.from_env:
        setup_from_env()
    else:
        print("🔐 Configuration des secrets Confluence")
        print("\nOptions disponibles:")
        print("  --interactive  : Configuration interactive")
        print("  --token TOKEN  : Utiliser un token spécifique")
        print("  --from-env     : Depuis variables d'environnement")
        print("  --list         : Lister les secrets existants")
        print("  --verify       : Vérifier les secrets")
        print("\nExemples:")
        print("  python scripts/setup_confluence_secrets.py --interactive")
        print("  python scripts/setup_confluence_secrets.py --from-env")
        print("  python scripts/setup_confluence_secrets.py --verify")


if __name__ == "__main__":
    main()
