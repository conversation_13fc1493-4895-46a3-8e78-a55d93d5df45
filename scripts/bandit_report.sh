#!/bin/bash +x

PROJECT_ID=$1
DATASET=$2
TABLE_NAME='reporting'
COMPONENT_REPORTING=$3

BANDIT_REPORT_FILE='bandit_report.csv'
BQ_OUTPUT_FILE='bandit_report_bq.csv'
HEADER_TMP_FILE='bq_report.csv'
DATA_TMP_FILE='data_bandit.csv'

echo PROJECT ID : $PROJECT_ID DATASET : $DATASET COMPONENT_REPORTING : $COMPONENT_REPORTING
HEADER=`head -n1 $BANDIT_REPORT_FILE`
echo -n date,component, > $HEADER_TMP_FILE
echo $HEADER >> $HEADER_TMP_FILE
grep -v $HEADER $BANDIT_REPORT_FILE  >> $DATA_TMP_FILE

DATETIME=`date +"%Y-%m-%d %H:%M:%S"`

# If the file from bandit is empty, then skip insertion
lines=$(< "$DATA_TMP_FILE" wc -l)
if [[ "$lines" != "0" ]]; then
echo Upload and insert Data to BigQuery
cat $DATA_TMP_FILE | while read line; do echo $DATETIME,$COMPONENT_REPORTING,${line}; done >> $BQ_OUTPUT_FILE
else
echo Upload file with one line to say that everything is OK
echo $DATETIME,$COMPONENT_REPORTING,No issues,No issues,0,No issues,No issues,No issues,No issues,0,0,0,No issues,No issues >> $BQ_OUTPUT_FILE
fi

bq load --source_format=CSV $DATASET.$TABLE_NAME $BQ_OUTPUT_FILE