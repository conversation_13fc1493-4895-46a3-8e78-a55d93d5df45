#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de benchmark pour comparer les performances des différentes approches asyncio/synchrone.
"""

import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor
from typing import List, Callable
import requests
import aiohttp


class AsyncioBenchmark:
    """Classe pour benchmarker les différentes approches."""
    
    def __init__(self):
        self.results = {}
    
    def benchmark_sync_requests(self, urls: List[str], num_calls: int = 10) -> dict:
        """Benchmark avec requests synchrone."""
        print("🔄 Benchmark: Requests synchrone")
        
        def make_request(url):
            try:
                response = requests.get(url, timeout=5)
                return response.status_code
            except Exception as e:
                return f"Error: {e}"
        
        times = []
        for i in range(num_calls):
            start = time.time()
            results = [make_request(url) for url in urls]
            end = time.time()
            times.append(end - start)
            print(f"  Appel {i+1}: {end - start:.2f}s")
        
        return {
            'method': 'sync_requests',
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
    
    def benchmark_sync_with_threadpool(self, urls: List[str], num_calls: int = 10) -> dict:
        """Benchmark avec requests + ThreadPoolExecutor."""
        print("🔄 Benchmark: Requests + ThreadPoolExecutor")
        
        def make_request(url):
            try:
                response = requests.get(url, timeout=5)
                return response.status_code
            except Exception as e:
                return f"Error: {e}"
        
        times = []
        for i in range(num_calls):
            start = time.time()
            with ThreadPoolExecutor(max_workers=len(urls)) as executor:
                futures = [executor.submit(make_request, url) for url in urls]
                results = [future.result() for future in futures]
            end = time.time()
            times.append(end - start)
            print(f"  Appel {i+1}: {end - start:.2f}s")
        
        return {
            'method': 'sync_threadpool',
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
    
    def benchmark_async_aiohttp(self, urls: List[str], num_calls: int = 10) -> dict:
        """Benchmark avec aiohttp asynchrone."""
        print("🔄 Benchmark: aiohttp asynchrone")
        
        async def make_request(session, url):
            try:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return response.status
            except Exception as e:
                return f"Error: {e}"
        
        async def run_async_requests(urls):
            async with aiohttp.ClientSession() as session:
                tasks = [make_request(session, url) for url in urls]
                return await asyncio.gather(*tasks)
        
        times = []
        for i in range(num_calls):
            start = time.time()
            results = asyncio.run(run_async_requests(urls))
            end = time.time()
            times.append(end - start)
            print(f"  Appel {i+1}: {end - start:.2f}s")
        
        return {
            'method': 'async_aiohttp',
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
    
    def benchmark_mixed_approach_current(self, urls: List[str], num_calls: int = 10) -> dict:
        """Benchmark de l'approche mixte actuelle (loop.run_until_complete)."""
        print("🔄 Benchmark: Approche mixte actuelle")
        
        async def make_request(session, url):
            try:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return response.status
            except Exception as e:
                return f"Error: {e}"
        
        async def run_async_requests(urls):
            async with aiohttp.ClientSession() as session:
                tasks = [make_request(session, url) for url in urls]
                return await asyncio.gather(*tasks)
        
        def sync_wrapper(urls):
            # Simuler l'approche actuelle du ConfluenceLoader
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(run_async_requests(urls))
            finally:
                loop.close()
        
        times = []
        for i in range(num_calls):
            start = time.time()
            results = sync_wrapper(urls)
            end = time.time()
            times.append(end - start)
            print(f"  Appel {i+1}: {end - start:.2f}s")
        
        return {
            'method': 'mixed_current',
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
    
    def benchmark_mixed_approach_improved(self, urls: List[str], num_calls: int = 10) -> dict:
        """Benchmark de l'approche mixte améliorée (asyncio.run)."""
        print("🔄 Benchmark: Approche mixte améliorée")
        
        async def make_request(session, url):
            try:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return response.status
            except Exception as e:
                return f"Error: {e}"
        
        async def run_async_requests(urls):
            async with aiohttp.ClientSession() as session:
                tasks = [make_request(session, url) for url in urls]
                return await asyncio.gather(*tasks)
        
        def sync_wrapper_improved(urls):
            # Approche améliorée avec asyncio.run()
            return asyncio.run(run_async_requests(urls))
        
        times = []
        for i in range(num_calls):
            start = time.time()
            results = sync_wrapper_improved(urls)
            end = time.time()
            times.append(end - start)
            print(f"  Appel {i+1}: {end - start:.2f}s")
        
        return {
            'method': 'mixed_improved',
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
    
    def run_full_benchmark(self, urls: List[str] = None, num_calls: int = 5):
        """Exécute tous les benchmarks."""
        if urls is None:
            # URLs de test (services publics rapides)
            urls = [
                'https://httpbin.org/delay/0.1',
                'https://httpbin.org/delay/0.1',
                'https://httpbin.org/delay/0.1'
            ]
        
        print(f"🚀 Démarrage du benchmark avec {len(urls)} URLs et {num_calls} appels par méthode")
        print(f"URLs testées: {urls}")
        print("=" * 80)
        
        benchmarks = [
            self.benchmark_sync_requests,
            self.benchmark_sync_with_threadpool,
            self.benchmark_async_aiohttp,
            self.benchmark_mixed_approach_current,
            self.benchmark_mixed_approach_improved
        ]
        
        results = []
        for benchmark_func in benchmarks:
            try:
                result = benchmark_func(urls, num_calls)
                results.append(result)
                print(f"✅ {result['method']} terminé")
            except Exception as e:
                print(f"❌ Erreur dans {benchmark_func.__name__}: {e}")
            print("-" * 40)
        
        self.results = results
        self.print_summary()
        return results
    
    def print_summary(self):
        """Affiche un résumé des résultats."""
        print("\n📊 RÉSUMÉ DES PERFORMANCES")
        print("=" * 80)
        
        # Trier par temps moyen
        sorted_results = sorted(self.results, key=lambda x: x['avg_time'])
        
        print(f"{'Méthode':<20} {'Temps Moyen':<12} {'Min':<8} {'Max':<8} {'Écart-type':<10}")
        print("-" * 70)
        
        for result in sorted_results:
            print(f"{result['method']:<20} "
                  f"{result['avg_time']:.3f}s{'':<6} "
                  f"{result['min_time']:.3f}s{'':<2} "
                  f"{result['max_time']:.3f}s{'':<2} "
                  f"{result['std_dev']:.3f}s")
        
        print("\n🏆 RECOMMANDATIONS:")
        best = sorted_results[0]
        print(f"• Meilleure performance: {best['method']} ({best['avg_time']:.3f}s)")
        
        # Analyser les résultats
        sync_methods = [r for r in sorted_results if 'sync' in r['method']]
        async_methods = [r for r in sorted_results if 'async' in r['method']]
        mixed_methods = [r for r in sorted_results if 'mixed' in r['method']]
        
        if sync_methods:
            best_sync = min(sync_methods, key=lambda x: x['avg_time'])
            print(f"• Meilleure approche synchrone: {best_sync['method']} ({best_sync['avg_time']:.3f}s)")
        
        if mixed_methods and len(mixed_methods) >= 2:
            current = next((r for r in mixed_methods if 'current' in r['method']), None)
            improved = next((r for r in mixed_methods if 'improved' in r['method']), None)
            if current and improved:
                improvement = ((current['avg_time'] - improved['avg_time']) / current['avg_time']) * 100
                print(f"• Amélioration mixte: {improvement:.1f}% plus rapide avec asyncio.run()")


def main():
    """Fonction principale."""
    benchmark = AsyncioBenchmark()
    
    # URLs de test plus réalistes (simulant des appels API)
    test_urls = [
        'https://httpbin.org/delay/0.2',  # Simuler un appel API lent
        'https://httpbin.org/delay/0.1',  # Simuler un appel API rapide
        'https://httpbin.org/delay/0.15', # Simuler un appel API moyen
    ]
    
    try:
        results = benchmark.run_full_benchmark(test_urls, num_calls=3)
        
        print("\n💡 ANALYSE POUR CONFLUENCE LOADER:")
        print("• Si les appels Confluence sont I/O intensifs, l'approche asynchrone est optimale")
        print("• Pour la cohérence architecturale, l'approche synchrone + ThreadPoolExecutor est recommandée")
        print("• L'amélioration avec asyncio.run() offre un gain immédiat sans refactoring majeur")
        
    except KeyboardInterrupt:
        print("\n⏹️  Benchmark interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du benchmark: {e}")


if __name__ == "__main__":
    main()
