#!/bin/bash +x

PROJECT_ID=$1
DATASET=$2
TABLE_NAME='coverage'
COVERAGE_REPORT_FILE=$3
COMPONENT=$4

BQ_OUTPUT_FILE='coverage_report_bq.csv'
FINAL_TMP_FILE='data_coverage.csv'
[ -e $BQ_OUTPUT_FILE ] && rm $BQ_OUTPUT_FILE
[ -e $FINAL_TMP_FILE ] && rm $FINAL_TMP_FILE
tail -n +3 $COVERAGE_REPORT_FILE >> $FINAL_TMP_FILE


echo PROJECT ID : $PROJECT_ID,  DATASET : $DATASET, COVERAGE_REPORT_FILE : $COVERAGE_REPORT_FILE
echo HEADER : date,filename,number_line,non_tested_line,coverage_rate

DATETIME=`date +"%Y-%m-%d %H:%M:%S"`

echo Upload and insert Data to BigQuery
while read -r line;
do
    readline=$line
    if [[ ! $readline == *"-----"* ]] ; then
        line_reformated=$(echo ${line}| sed -e 's/\s\+/,/g')
        line_without_percent_character=$(echo $line_reformated | rev | cut -c2- | rev)
        echo $DATETIME,$COMPONENT,${line_without_percent_character} >> $BQ_OUTPUT_FILE
    fi
done < $FINAL_TMP_FILE

bq load --source_format=CSV $DATASET.$TABLE_NAME $BQ_OUTPUT_FILE

echo Data inserted to Bigquery
