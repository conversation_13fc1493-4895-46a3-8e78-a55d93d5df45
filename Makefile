PYTHON_VERSION=3.12
ROOT_DIR:=$(realpath $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST)))))
.PHONY: init start unit-tests flake bandit
BACK_PORT=8091
LOAD_SCHEDULER_PORT=8092
EMBEDDING_PORT=8093
RETRIEVAL_PORT=8094


init:
	python -V
	pip -V
	pip install pipenv --upgrade
	python -m pip install --upgrade pip
	pipenv --python ${PYTHON_VERSION}
	pipenv run pip install -r requirements.txt
	pipenv run pip install -r tests/test-requirements.txt


start:
	pipenv run pip freeze | grep storage
	export ENV="local" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-dev" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/local && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev" && \
	export URL_SERVICE_BASIC="https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export OKAPI_URL_BASIC="https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token" && \
	export SERVICE_SCOPE_BASIC="api-newbasicqualif-v1-int:readonly" && \
	export TIMEOUT_BASIC=120 && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT}

# Keep for use okapi
#   export KBOT_BACK_API_URL='https://enabler-knowledge-bot-dev.dev.api.hbx.geo.infra.ftgroup' && \
#   export OKAPI_URL='https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token' && \
#   export KBOT_LOADSCHEDULER_CLIENT_ID="kbot_loadscheduler_client_id" && \
#   export KBOT_LOADSCHEDULER_CLIENT_SECRET="kbot_loadscheduler_client_secret" && \
#   export KBOT_LOADSCHEDULER_CLIENT_SCOPE='api-enabler_knowledge_bot_dev-v1-dev:admin_all' && \




## Runs unit tests for src
## KBOT_BACK_API_URL : associated to tests/data/kbot_back_api_test_data
## KBOT_WORK_BUCKET_PREFIX : associated to bucket given to mock-gcs
unit-tests:
	export ENV="tests" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-test" && \
	export ROOT_TEST=${ROOT_DIR}/tests && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_URL="https://kbot-back-api:8080" && \
	export KBOT_EMBEDDING_API_URL="https://kbot-embedding-api:8081" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]" && \
	pipenv run python -m pytest -v --cov-config=.coveragerc --cov-report=html --cov-report term --cov-report=xml --cov=src/ tests

flake:
	rm -f flake8_report_src.txt && \
	pipenv run flake8 --output-file=flake8_report_src.txt --format=pylint --tee --exit-zero --statistics src && \
	if [ ! -s flake8_report_src.txt ]; then echo "SCAN FLAKE8 OK FOR SRC DIRECTORY"; fi && \
	rm -f flake8_report_tests.txt && \
	pipenv run flake8 --output-file=flake8_report_tests.txt --format=pylint --tee --exit-zero --statistics tests && \
	if [ ! -s flake8_report_tests.txt ]; then echo "SCAN FLAKE8 OK FOR TESTS DIRECTORY"; fi

bandit:
	touch bandit_report.csv
	pipenv run bandit -c bandit.yaml -f csv -o bandit_report.csv -r src --exit-zero
	cat bandit_report.csv
