# Use an official lightweight Python image.
FROM python:3.12-alpine

# To keep until CVE-2024-9143 is corrected on base image
RUN apk upgrade --no-cache

# Set the working directory to /app
WORKDIR /app
# Copy the current directory contents into the container at /app
COPY requirements.txt src/ ./

# Install any needed packages. specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Make port 8080 available to the world outside this container
EXPOSE 8080
# Command to run the application using Uvicorn
CMD ["uvicorn", "kbotloadscheduler.main:app", "--host", "0.0.0.0", "--port", "8080"]

# To build image localy
# docker build . -t kbot-loadscheduler
# To run image localy
# bash runDockerLocal
# And go to http://localhost:8080/docs
