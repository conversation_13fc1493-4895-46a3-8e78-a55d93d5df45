# Guide de Débogage pour kbot-load-scheduler

Ce guide a pour objectif d'aider les développeurs et les opérateurs à diagnostiquer et résoudre les problèmes rencontrés avec `kbot-load-scheduler`.

## Table des Matières

1.  [Principes Généraux de Débogage](#1-principes-généraux-de-débogage)
2.  [Comprendre les Logs](#2-comprendre-les-logs)
3.  [Débogage du Flux d'Orchestration](#3-débogage-du-flux-dorchestration)
    *   [Inspecter les Fichiers d'État sur GCS](#inspecter-les-fichiers-détat-sur-gcs)
    *   [Suivre une Tâche Spécifique](#suivre-une-tâche-spécifique)
4.  [Problèmes Courants et Solutions](#4-problèmes-courants-et-solutions)
    *   [Chargement d'un Périmètre ne Démarre Pas](#chargement-dun-périmètre-ne-démarre-pas)
    *   [Tâches Bloquées en `.inprogress`](#tâches-bloquées-en-inprogress)
    *   [Erreurs d'Authentification aux Sources](#erreurs-dauthentification-aux-sources)
    *   [Problèmes avec `kbot-back` ou `kbot-embedding`](#problèmes-avec-kbot-back-ou-kbot-embedding)
    *   [Problèmes Spécifiques aux Loaders](#problèmes-spécifiques-aux-loaders)
5.  [Outils et Techniques de Débogage](#5-outils-et-techniques-de-débogage)
    *   [Exécution Locale](#exécution-locale)
    *   [Tester les Endpoints API](#tester-les-endpoints-api)
    *   [Utilisation de `gsutil`](#utilisation-de-gsutil)
    *   [Débogueur Python](#débogueur-python)
6.  [Débogage du Loader Confluence](#6-débogage-du-loader-confluence)

---

## 1. Principes Généraux de Débogage

*   **Isoler le problème :** Déterminez quelle partie du système est affectée (un périmètre spécifique, une source, une étape du chargement, une API externe).
*   **Consulter les logs :** Les logs sont votre source d'information la plus précieuse. Activez le niveau `DEBUG` si nécessaire pour plus de détails.
*   **Vérifier les configurations :** Assurez-vous que les variables d'environnement, les configurations de source et les secrets sont corrects.
*   **Reproductibilité :** Essayez de reproduire le problème de manière constante, si possible dans un environnement de test ou localement.
*   **Commencer par le début du flux :** Si un document n'est pas embeddé, vérifiez d'abord si la source a été listée, puis si le document a été récupéré, etc.

## 2. Comprendre les Logs

`kbot-load-scheduler` utilise une journalisation structurée. Chaque message de log important devrait inclure :

*   `timestamp`: Date et heure de l'événement.
*   `level`: Niveau du log (INFO, WARNING, ERROR, DEBUG).
*   `logger`: Nom du module logger (ex: `kbotloadscheduler.service.schedule_service`).
*   `correlation_id`: Identifiant unique pour suivre une requête ou un flux de traitement à travers différents appels et services. **Utilisez cet ID pour filtrer les logs et suivre une opération spécifique.**
*   `message`: Le message de log principal.
*   `module`, `function`, `line`: Contexte d'où le log a été émis.
*   Autres champs contextuels : `perimeter_code`, `source_code`, `document_id`, `file_path`, `url_called`, etc.

**Exemple de log d'erreur :**

```json
{
  "timestamp": "2023-10-27T10:30:05.123Z",
  "level": "ERROR",
  "logger": "kbotloadscheduler.service.loader_service",
  "correlation_id": "abcdef12-3456-7890-fedc-ba9876543210",
  "message": "Erreur lors de la récupération du document: Fichier non trouvé sur la source",
  "module": "loader_service",
  "function": "get_document",
  "line": 150,
  "perimeter_code": "main_perimeter",
  "document_get_file": "gs://my-bucket-main_perimeter/202310271000/mydomain/mysource/getdoc/doc123.getdoc.json",
  "exception": {
    "type": "LoaderException",
    "message": "File /path/to/doc123 doesn't exists",
    "traceback": "..."
  }
}
```

**Activation du niveau DEBUG :**

Pour un débogage plus fin, vous pouvez changer le niveau de log en `DEBUG`.
*   **Localement :** Modifiez la variable `DEBUG_LOG` (ou `LOG_LEVEL` si implémenté) dans votre environnement ou `Makefile`.
*   **Cloud Run :** Mettez à jour les variables d'environnement du service Cloud Run.

## 3. Débogage du Flux d'Orchestration

Le flux d'orchestration repose heavily sur des fichiers d'état stockés dans Google Cloud Storage (GCS) dans le bucket de travail (`KBOT_WORK_BUCKET_PREFIX`).

### Inspecter les Fichiers d'État sur GCS

Le bucket de travail est structuré comme suit :
`gs://{KBOT_WORK_BUCKET_PREFIX}/{perimeter_code}/{load_date}/{domain_code}/{source_code}/{file_type}/`

*   **`{load_date}`:** Un timestamp au format `YYYYMMDDHHMM` indiquant quand le cycle de chargement pour cette source a été initié (par l'appel à `/sources/load/...`).
*   **`{file_type}`:** Peut être `getlist`, `list`, `getdoc`, `docs`, `removedoc`.

**Fichiers importants à inspecter :**

*   **`.../{file_type}/{file_type}.json`**: Le fichier de tâche principal pour une étape.
    *   `getlist/getlist.json`: Contient la `SourceBean` à traiter.
    *   `list/list.json`: Contient la liste des `DocumentBean` de la source.
    *   `getdoc/{doc_id}.getdoc.json`: Contient la `SourceBean` et le `DocumentBean` spécifique à récupérer.
    *   `docs/{doc_id}.metadata.json`: Contient les métadonnées du document récupéré, prêt pour l'embedding.
    *   `removedoc/removedoc.json`: Contient la liste des `DocumentBean` à supprimer.
*   **`.../{file_type}/{file_type}.inprogress`**: Fichier vide indiquant que la tâche est en cours de traitement. Si ce fichier persiste longtemps sans `.done` ou `.error`, la tâche est peut-être bloquée.
*   **`.../{file_type}/{file_type}.done`**: Fichier vide indiquant que la tâche a été complétée avec succès.
*   **`.../{file_type}/{file_type}.error`**: Fichier contenant le message d'erreur si la tâche a échoué.
*   **`.../docs_done/` et `.../docs_error/`**: Répertoires où les fichiers `.metadata.json` sont déplacés après l'étape d'embedding.
*   **`.../tokens/{perimeter_code}`**: Fichier de verrou pour un périmètre. S'il existe, un traitement pour ce périmètre est (ou était récemment) en cours.

**Utilisation de `gsutil` :**

```bash
# Lister les répertoires de load_date pour un périmètre
gsutil ls gs://{KBOT_WORK_BUCKET_PREFIX}/my_perimeter/

# Lister les fichiers pour une source et un type de tâche spécifique
gsutil ls gs://{KBOT_WORK_BUCKET_PREFIX}/my_perimeter/202310271000/mydomain/mysource/getlist/

# Afficher le contenu d'un fichier de tâche
gsutil cat gs://{KBOT_WORK_BUCKET_PREFIX}/my_perimeter/202310271000/mydomain/mysource/getlist/getlist.json

# Vérifier si un fichier .error existe
gsutil ls gs://{KBOT_WORK_BUCKET_PREFIX}/my_perimeter/202310271000/mydomain/mysource/getdoc/doc123.getdoc.error
```

### Suivre une Tâche Spécifique

1.  **Identifier le `load_date`** : Si un chargement a été déclenché, il y aura un répertoire avec un timestamp récent.
2.  **Vérifier `getlist/getlist.json`** : Ce fichier est le point de départ. Il contient la configuration de la source.
3.  **Suivre les étapes :**
    *   Si `getlist.json` existe mais pas `getlist.done`, l'appel à `/loader/list/{perimeter}` a échoué ou n'a pas été déclenché. Vérifiez les logs du `ScheduleService` et du `LoaderService`.
    *   Si `getlist.done` existe, vérifiez `list/list.json`.
    *   Si `list.json` existe mais pas `list.done`, l'appel à `/document/compare/{perimeter}` est en cause. Vérifiez les logs du `DocumentService`.
    *   Continuez ainsi pour `getdoc/`, `docs/`, et `removedoc/`.
4.  **Logs avec `correlation_id`** : Les logs du `ScheduleService` pour un appel à `/schedule/treatments/.../launch` auront un `correlation_id`. Utilisez cet ID pour suivre tous les appels API et traitements internes déclenchés par cette exécution.

## 4. Problèmes Courants et Solutions

### Chargement d'un Périmètre ne Démarre Pas

*   **Vérifier Cloud Scheduler :** Le job Cloud Scheduler pour ce périmètre est-il activé et correctement configuré (URL, compte de service OIDC) ?
*   **Vérifier le token de verrou :** Y a-t-il un fichier `gs://{KBOT_WORK_BUCKET_PREFIX}/tokens/{perimeter_code}` ? S'il est très ancien (plus de `ScheduleService.TOKEN_LIFE`), il peut être supprimé manuellement (avec précaution).
*   **Logs de `ScheduleService` :** Lors de l'appel à `/schedule/treatments/.../launch`, que disent les logs ? Y a-t-il des erreurs ?
*   **Aucune source configurée pour le périmètre :** L'appel à `KbotBackApi` (via `SourcesService`) renvoie-t-il des sources pour ce périmètre ? Vérifiez la configuration dans `kbot-back`.

### Tâches Bloquées en `.inprogress`

*   Une tâche (ex: `getlist.json`) a son fichier `.inprogress` mais ni `.done` ni `.error` après un temps anormalement long.
*   **Cause possible :** Le pod Cloud Run a redémarré ou crashé pendant le traitement.
*   **Solution :**
    1.  Inspectez les logs Cloud Run pour des signes de crash (OOM, etc.).
    2.  Le `ScheduleService` devrait normalement ignorer les tâches `.inprogress` pendant un certain temps. Si le fichier `.inprogress` est très ancien, il peut être nécessaire de le supprimer manuellement (ou de le renommer en `.error` après analyse) pour permettre au `ScheduleService` de le reprendre. *Faites cela avec précaution.*
    3.  Analysez le contenu du fichier `.json` associé pour comprendre quelle tâche était en cours.

### Erreurs d'Authentification aux Sources

*   **Logs :** Recherchez des erreurs `AuthenticationError` ou des codes HTTP 401/403 dans les logs du loader spécifique (ex: `ConfluenceLoader`, `SharePointLoader`).
*   **Secrets :**
    *   Vérifiez que les secrets sont correctement configurés dans Google Secret Manager (ou localement pour les tests via `conf/etc/secrets/`).
    *   Assurez-vous que le compte de service de Cloud Run a les permissions IAM pour accéder à ces secrets (`Secret Manager Secret Accessor`).
    *   Vérifiez que les noms des secrets correspondent à ce qu'attend `ConfigWithSecret` (ex: `{perimeter_code}-confluence-credentials`).
    *   Vérifiez que le contenu des secrets est correct (bon token, bon format JSON si attendu).
*   **Permissions sur la source externe :** Le compte/token utilisé a-t-il les permissions nécessaires sur Confluence, SharePoint, etc. ?

### Problèmes avec `kbot-back` ou `kbot-embedding`

*   **Logs :** Recherchez des erreurs lors des appels à ces API (ex: `KbotBackApi`, `KbotEmbeddingApi`). Les logs indiqueront souvent l'URL appelée et le code de statut HTTP.
*   **Disponibilité des services :** Ces services sont-ils opérationnels ?
*   **Authentification :**
    *   Pour `KbotBackApi` : Problèmes avec Okapi JWT (`orange_jwt.py`). Le sidecar proxy est-il fonctionnel ? Les secrets `kbot_loadscheduler_client_id/secret` sont-ils corrects ?
    *   Pour `KbotEmbeddingApi` (et autres appels Cloud Run) : Problèmes avec `build_auth_header.py` (token OIDC). Le compte de service a-t-il le rôle "Service Invoker" sur le service cible ?
*   **Connectivité réseau :** Depuis Cloud Run, la connectivité vers ces services est-elle assurée (VPC, etc.) ?

### Problèmes Spécifiques aux Loaders

*   **ConfluenceLoader :**
    *   Consultez le `README.md` et les guides de débogage spécifiques au module Confluence.
    *   Les problèmes de PAT (expiré, permissions insuffisantes) sont courants.
    *   Le CQL généré est-il correct ?
    *   Problèmes de pagination ou de gestion des pièces jointes volumineuses.
*   **SharePointLoader :**
    *   Configuration du certificat client et de la clé privée.
    *   Syntaxe des URLs relatives (`ServerRelativeUrl`).
*   **BasicLoader :**
    *   L'API externe répond-elle correctement ?
    *   Le pré-traitement et la conversion Markdown fonctionnent-ils comme prévu ?
*   **GCSLoader :**
    *   Permissions sur les buckets source et destination.
    *   Chemins de fichiers corrects.

## 5. Outils et Techniques de Débogage

### Exécution Locale

*   Utilisez `make start` pour lancer le service localement.
*   Configurez votre `.env` et votre dossier `conf/etc/secrets/local/` pour pointer vers des instances de test ou des mocks.
*   Utilisez un client HTTP (Postman, `curl`) pour appeler les endpoints et observer le comportement.

### Tester les Endpoints API

*   Utilisez `curl` ou Postman.
    *   **`/schedule/treatments/{perimeter}/launch`**: Pour simuler un déclenchement par Cloud Scheduler.
        ```bash
        curl -X POST http://localhost:8080/schedule/treatments/my_perimeter/launch
        ```
    *   **Endpoints granulaires** (ex: `/loader/list/{perimeter}`): Préparez les fichiers JSON d'entrée nécessaires (ex: un `getlist.json`) et placez-les sur un bucket GCS accessible (ou simulez avec un mock GCS pour les tests).
        ```bash
        # Exemple pour /loader/list
        # 1. Créez un fichier local input_getlist.json:
        #    {"get_list_file": "gs://your-test-bucket/my_perimeter/202301010000/testdomain/testsource/getlist/getlist.json"}
        # 2. Assurez-vous que le fichier sur GCS existe et est valide.
        curl -X POST http://localhost:8080/loader/list/my_perimeter \
             -H "Content-Type: application/json" \
             -d @input_getlist.json
        ```

### Utilisation de `gsutil`

Indispensable pour inspecter et manipuler les fichiers d'état sur GCS (voir section 3.1).

### Débogueur Python

*   Si vous exécutez localement, vous pouvez utiliser `pdb` ou le débogueur de votre IDE.
*   Placez des points d'arrêt (`breakpoint()`) dans les services ou loaders pour inspecter les variables et le flux d'exécution.

## 6. Débogage du Loader Confluence

Le loader Confluence est un sous-système complexe avec sa propre documentation de débogage. Référez-vous à :

*   `src/kbotloadscheduler/loader/confluence/README.md` (sections Troubleshooting)
*   `src/kbotloadscheduler/loader/confluence/docs/` pour des guides plus détaillés sur ses composants internes (si disponibles).

**Points clés pour Confluence :**

*   **Vérification des logs spécifiques à Confluence :** Ils contiennent des informations sur les appels CQL, la pagination, le traitement des pièces jointes.
*   **Fichiers de suivi locaux :** Si `STORAGE_TYPE=filesystem` est utilisé pour le module Confluence (par exemple en développement standalone), inspectez `.confluence_rag_data/` pour les hashes et l'historique de synchronisation.
*   **Configuration `SearchCriteria` :** Assurez-vous que les espaces, labels, etc., sont corrects.
*   **Permissions du token PAT :** Le token utilisé doit avoir les permissions de lecture suffisantes sur les espaces Confluence ciblés.

---

Ce guide est un point de départ. N'hésitez pas à l'enrichir avec les problèmes et solutions que vous découvrez.