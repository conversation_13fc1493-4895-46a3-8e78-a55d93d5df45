from kbotloadscheduler.apicall.kbot_back_api import KbotBackApi
from data.kbot_back_api_test_data import KbotBackApiTestData
from kbotloadscheduler.bean.beans import SourceBean


class TestKbotBackApi:

    def test_get_perimeters(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        actual_perimeter_list = kbot_back_api.get_perimeters()

        adapter = kbot_back_api_test_data.adapters.get('/perimeter/list')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_perimeter_list == list(kbot_back_api_test_data.PERIMETERS_LIST.values())

    def test_get_perimeter_by_code(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        actual_perimeter = kbot_back_api.get_perimeter_by_code('perimB')

        adapter = kbot_back_api_test_data.adapters.get('/perimeter/bycode/perimB')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_perimeter == kbot_back_api_test_data.PERIMETERS_LIST[2]

    def test_get_perimeter_domains(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        actual_domains_list = kbot_back_api.get_perimeter_domains(2)

        adapter = kbot_back_api_test_data.adapters.get('/perimeter/2/domains')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_domains_list == kbot_back_api_test_data.DOMAINS_LIST[2]

    def test_get_domain_sources_to_load(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        actual_sources_list = kbot_back_api.get_domain_sources_to_load(22)

        adapter = kbot_back_api_test_data.adapters.get('/domain/22/sourcestoload')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_sources_list == kbot_back_api_test_data.SOURCES_LIST[22]

    def test_get_sources_by_perimeter_code(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        actual_sources_list = kbot_back_api.get_sources('perimB')

        expected_sources = []
        expected_sources.extend([SourceBean(**s, domain_code='domBa', perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([SourceBean(**s, domain_code='domBb', perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([SourceBean(**s, domain_code='domBc', perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])
        assert actual_sources_list == expected_sources

    def test_set_source_done(self, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)

        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        kbot_back_api.set_source_done(123)

        adapter = kbot_back_api_test_data.adapters.get('/source/123/done')
        assert adapter.called
        assert adapter.call_count == 1
