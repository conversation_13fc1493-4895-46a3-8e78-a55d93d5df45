from kbotloadscheduler.apicall.kbot_embedding_api import KbotEmbeddingApi
from kbotloadscheduler.bean.beans import DocumentBean
from data.kbot_embedding_api_test_data import KbotEmbeddingApiTestData


class TestKbotEmbeddingApi:

    def test_get_document_list(self, requests_mock):
        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        kbot_embedding_api = KbotEmbeddingApi(kbot_embedding_api_test_data.KBOT_EMBEDDING_API_URL)
        actual_documents_list = kbot_embedding_api.get_document_list(
            kbot_embedding_api_test_data.PERIMETER_CODE,
            kbot_embedding_api_test_data.DOMAIN_CODE,
            kbot_embedding_api_test_data.SOURCE_CODE
        )

        adapter = kbot_embedding_api_test_data.adapters.get(kbot_embedding_api_test_data.get_get_docs_url())
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_documents_list == \
               kbot_embedding_api_test_data.get_embedded_documents_bean()

    def test_embedd_document(self, requests_mock):
        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        kbot_embedding_api = KbotEmbeddingApi(kbot_embedding_api_test_data.KBOT_EMBEDDING_API_URL)
        actual_result = kbot_embedding_api.embedd_document(
            'perimOK',  # Perimetre pour déclencher une réponse ok
            metadata_file='gs://le-bucket/path/dont/care/its/not/use/for/tests/file.metadata.json'
        )

        adapter = kbot_embedding_api_test_data.adapters.get('/embedded_documents/perimOK')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_result == \
               {'status': 'ok',
                'document': DocumentBean.from_embedding_api(kbot_embedding_api_test_data.DOCUMENT_FROM_EMBEDDING)}

    def test_embedd_document_ko(self, requests_mock):
        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        kbot_embedding_api = KbotEmbeddingApi(kbot_embedding_api_test_data.KBOT_EMBEDDING_API_URL)
        actual_document = None
        try:
            actual_document = kbot_embedding_api.embedd_document(
                'perimKO',  # Perimetre pour déclencher une réponse ko
                metadata_file='gs://le-bucket/path/dont/care/its/not/use/for/tests/file.metadata.json'
            )
            assert False, "an KbotEmbeddingApi.EmbeddingException must be raised"
        except KbotEmbeddingApi.EmbeddingException as e:
            assert True
        finally:
            adapter = kbot_embedding_api_test_data.adapters.get('/embedded_documents/perimKO')
            assert adapter.called
            assert adapter.call_count == 1

    def test_remove_document(self, requests_mock):
        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        kbot_embedding_api = KbotEmbeddingApi(kbot_embedding_api_test_data.KBOT_EMBEDDING_API_URL)
        actual_result = kbot_embedding_api.remove_document(
            'perimOK',  # Perimetre pour déclencher une réponse ok
            document=DocumentBean.from_embedding_api(kbot_embedding_api_test_data.DOCUMENT_FROM_EMBEDDING)
        )
        document_id = kbot_embedding_api_test_data.DOCUMENT['id']
        adapter = kbot_embedding_api_test_data.adapters.get(f'/embedded_documents'
                                                            f'?perimeter=perimOK&document_id={document_id}')
        assert adapter.called
        assert adapter.call_count == 1
        assert actual_result is True
