import json
from datetime import datetime
from typing import List
from kbotloadscheduler.bean.beans import DocumentBean, Metadata


class KbotEmbeddingApiTestData:

    DATE_CREATION = datetime(2024, 8, 11, 8, 12, 42)
    DATE_MODIF = datetime(2024, 9, 11, 8, 12, 42)
    DATE_CREATION_STR = '20240811081242'
    DATE_MODIF_STR = '20240911081242'

    KBOT_EMBEDDING_API_URL = 'https://kbot-embedding-api:8081'
    LOAD_DATE = "202409121430"
    PERIMETER_CODE = "perimP"
    DOMAIN_CODE = "domB"
    SOURCE_CODE = "src5"
    CONF_JSON = {'conf1': 'val1', 'conf2': 'val2'}
    SOURCE = {
        'id': 555, 'code': SOURCE_CODE, 'label': 'source 5', 'src_type': 'gcs',
        'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483, 'load_interval': 24,
        'domain_code': DOMAIN_CODE, 'force_embedding': False, 'perimeter_code': PERIMETER_CODE
    }
    SOURCE_WITH_FORCE_EMBEDDING = {
        'id': 555, 'code': SOURCE_CODE, 'label': 'source 5', 'src_type': 'gcs',
        'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483, 'load_interval': 24,
        'domain_code': DOMAIN_CODE, 'force_embedding': True, 'perimeter_code': PERIMETER_CODE
    }

    REPO_BUCKET = 'repo-bucket'
    BASE_BUCKET = 'mon_bucket-[perimeter_code]'
    WORK_BUCKET = f'mon_bucket-{PERIMETER_CODE}'
    RELATIVE_OUTPUT = f'{LOAD_DATE}/{DOMAIN_CODE}/{SOURCE_CODE}'
    DOCUMENT_NAME = 'src1/domainA/chemin/doc_for_test.txt'
    DOCUMENT = {
        'id': f'{DOMAIN_CODE}|{SOURCE_CODE}|{DOCUMENT_NAME}',
        'modification_time': '20240915122436',
        'name': '',
        'path': '',
    }
    DOCUMENT_FROM_EMBEDDING = {
        'document_id': f'{DOMAIN_CODE}|{SOURCE_CODE}|{DOCUMENT_NAME}',
        'modification_date': '2024-09-15T12:24:36',
        'name': '',
        'path': ''
    }
    CONF_SOURCE = {
        'bucket': f'gs://{REPO_BUCKET}',
        'prefix': 'src1/domainA',
        'sa': '<EMAIL>'
    }
    METADATA = {
        Metadata.DOCUMENT_ID: DOCUMENT['id'],
        Metadata.DOCUMENT_NAME: DOCUMENT_NAME,
        Metadata.DOMAIN_CODE: DOMAIN_CODE,
        Metadata.SOURCE_CODE: SOURCE_CODE,
        Metadata.SOURCE_TYPE: 'gcs',
        Metadata.SOURCE_CONF: json.dumps(CONF_SOURCE),
        Metadata.SOURCE_URL: f'gs://{REPO_BUCKET}/{DOCUMENT_NAME}',
        Metadata.LOCATION: f'gs://{WORK_BUCKET}/{RELATIVE_OUTPUT}/docs/{DOCUMENT_NAME}',
        Metadata.CREATION_TIME: DATE_CREATION,
        Metadata.MODIFICATION_TIME: DATE_MODIF
    }
    METADATA_SERIALIZED = {
        **METADATA,
        Metadata.CREATION_TIME: DATE_CREATION_STR,
        Metadata.MODIFICATION_TIME: DATE_MODIF_STR
    }

    def __init__(self):
        self.adapters = {}

    def set_test_data(self, mock):
        self.set_embedded_documents(mock)
        self.set_embed_url(mock)
        self.set_remove_url_for_api_test(mock)
        self.set_remove_url_for_service_test(mock)

    def set_embedded_documents(self, mock):
        documents = self.get_embedded_documents()
        url = self.get_get_docs_url()
        self.adapters[url] = mock.get(self.KBOT_EMBEDDING_API_URL + url,
                                      text=json.dumps(documents))

    def set_embed_url(self, mock):
        url = '/embedded_documents/perimOK'
        self.adapters[url] = mock.post(self.KBOT_EMBEDDING_API_URL + url,
                                       text=json.dumps(self.DOCUMENT_FROM_EMBEDDING))
        url = '/embedded_documents/perimKO'
        self.adapters[url] = mock.post(self.KBOT_EMBEDDING_API_URL + url,
                                       status_code=505, text='message d''erreur')

    def set_remove_url_for_api_test(self, mock):
        url = f'/embedded_documents?perimeter=perimOK' \
              f'&document_id={self.DOMAIN_CODE}|{self.SOURCE_CODE}|{self.DOCUMENT_NAME}'
        self.adapters[url] = mock.delete(self.KBOT_EMBEDDING_API_URL + url,
                                         text='ok')
        url = f'/embedded_documents?perimeter=perimKO' \
              f'&document_id={self.DOMAIN_CODE}|{self.SOURCE_CODE}|{self.DOCUMENT_NAME}'
        self.adapters[url] = mock.delete(self.KBOT_EMBEDDING_API_URL + url,
                                         status_code=505, text="message d'erreur")

    def set_remove_url_for_service_test(self, mock):
        for document in self.get_documents_to_remove():
            document_id = document['id']
            url = f'/embedded_documents?perimeter=perimOK&document_id={document_id}'
            self.adapters[url] = mock.delete(self.KBOT_EMBEDDING_API_URL + url,
                                             text='ok')
            url = f'/embedded_documents?perimeter=perimKO&document_id={document_id}'
            self.adapters[url] = mock.delete(self.KBOT_EMBEDDING_API_URL + url,
                                             status_code=505, text="message d'erreur")

    def get_get_docs_url(self):
        return f'/embedded_documents?perimeter={self.PERIMETER_CODE}' \
               f'&domain={self.DOMAIN_CODE}&source={self.SOURCE_CODE}'

    # On liste les documents embedded : index de 1 à 6 inclus avec date modif en 2024091{index}
    def get_embedded_documents(self, from_embedding: bool = True) -> List[dict]:
        response_json = []
        for index in range(1, 7):
            name = f'/dir/{self.SOURCE_CODE}/{self.DOMAIN_CODE}/doc_{str(index)}.txt'
            id_key = 'id'
            date_key = 'modification_time'
            date_val = f'2024091{str(index)}122436'
            if from_embedding:
                id_key = 'document_id'
                date_key = 'modification_date'
                date_val = f'2024-09-1{str(index)}T12:24:36'
            response_json.append({
                id_key: f'{self.DOMAIN_CODE}|{self.SOURCE_CODE}|{name}',
                date_key: date_val
            })
        return response_json

    def get_embedded_documents_bean(self) -> List[DocumentBean]:
        return [DocumentBean.from_serializable_dict(document) for document in self.get_embedded_documents(False)]

    # Document du repo :
    #   - index 1 et 2 pas dans le repo
    #   - index 3 et 4 dans le repo avec même date
    #   - index 5 et 6 dans le repo avec dates plus récentes
    #   - index 7 et 8 non embedded (donc date on s'en fout)
    def get_repo_documents(self):
        response_json = []
        # Dans embedded le prefixe est toujours 2024091
        prefix_date = {
            3: '2024091', 4: '2024091',
            5: '2024092', 6: '2024092',
            7: '2024090', 8: '2024090'
        }
        for index in range(3, 9):
            name = f'/dir/{self.SOURCE_CODE}/{self.DOMAIN_CODE}/doc_{str(index)}.txt'
            date = prefix_date[index]
            response_json.append({
                'id': f'{self.DOMAIN_CODE}|{self.SOURCE_CODE}|{name}',
                'name': name,
                'path': "",
                'modification_time': f'{date}{str(index)}122436'
            })
        return response_json

    # Document que l'on doit récupérer : 5, 6, 7 et 8
    #   - index 5 et 6 dans le repo avec dates plus récentes
    #   - index 7 et 8 non embedded (donc date on s'en fout)
    def get_documents_to_get(self):
        response_json = []
        # Dans embedded le prefixe est toujours 2024091
        prefix_date = {
            5: '2024092', 6: '2024092',
            7: '2024090', 8: '2024090'
        }
        for index in range(5, 9):
            name = f'/dir/{self.SOURCE_CODE}/{self.DOMAIN_CODE}/doc_{str(index)}.txt'
            date = prefix_date[index]
            response_json.append({
                'id': f'{self.DOMAIN_CODE}|{self.SOURCE_CODE}|{name}',
                'name': name,
                'path': '',
                'modification_time': f'{date}{str(index)}122436'
            })
        return response_json

    # Document que l'on doit supprimer : 1, 2
    def get_documents_to_remove(self):
        response_json = []
        # Dans embedded le prefixe est toujours 2024091
        for index in range(1, 3):
            name = f'/dir/{self.SOURCE_CODE}/{self.DOMAIN_CODE}/doc_{str(index)}.txt'
            response_json.append({
                'id': f'{self.DOMAIN_CODE}|{self.SOURCE_CODE}|{name}',
                'name': '',
                'path': '',
                'modification_time': f'2024091{str(index)}122436'
            })
        return response_json
