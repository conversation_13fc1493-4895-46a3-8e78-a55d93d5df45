import json


class KbotBackApiTestData:

    KBOT_BACK_API_URL = 'https://kbot-back-api:8080'

    PERIMETERS_LIST = {
        1: {"id": 1, "code": "perimA", "label": "Périmètre A"},
        2: {"id": 2, "code": "perimB", "label": "Périmètre B"},
        3: {"id": 3, "code": "perimC", "label": "Périmètre C"}
    }

    DOMAINS_LIST = {
        1: [
            {"id": 11, "code": "domAa", "label": "Domain A a"},
            {"id": 12, "code": "domAb", "label": "Domain A b"},
            {"id": 13, "code": "domAc", "label": "Domain A c"}
        ],
        2: [
            {"id": 21, "code": "domBa", "label": "Domain B a"},
            {"id": 22, "code": "domBb", "label": "Domain B b"},
            {"id": 23, "code": "domBc", "label": "Domain B c"}
        ],
        3: [
            {"id": 31, "code": "domCa", "label": "Domain C a"},
            {"id": 32, "code": "domCb", "label": "Domain C b"},
            {"id": 33, "code": "domCc", "label": "Domain C c"}
        ],
    }

    CONF_JSON = {'conf1': 'val1', 'conf2': 'val2'}
    SOURCES_LIST = {
        11: [
            {'id': 111, 'code': 'srcAa1', 'label': 'source A a 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 11, 'force_embedding': False},
            {'id': 112, 'code': 'srcAa2', 'label': 'source A a 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 11, 'force_embedding': False},
            {'id': 113, 'code': 'srcAa3', 'label': 'source A a 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 11, 'force_embedding': False}
        ],
        12: [
            {'id': 121, 'code': 'srcAb1', 'label': 'source A b 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 12, 'force_embedding': False},
            {'id': 122, 'code': 'srcAb2', 'label': 'source A b 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 12, 'force_embedding': False},
            {'id': 123, 'code': 'srcAb3', 'label': 'source A b 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 12, 'force_embedding': False}
        ],
        13: [
            {'id': 131, 'code': 'srcAc1', 'label': 'source A c 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 13, 'force_embedding': False},
            {'id': 132, 'code': 'srcAc2', 'label': 'source A c 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 13, 'force_embedding': False},
            {'id': 133, 'code': 'srcAc3', 'label': 'source A c 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 13, 'force_embedding': False}
        ],
        21: [
            {'id': 211, 'code': 'srcBa1', 'label': 'source B a 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 21, 'force_embedding': False},
            {'id': 212, 'code': 'srcBa2', 'label': 'source B a 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 21, 'force_embedding': False},
            {'id': 213, 'code': 'srcBa3', 'label': 'source B a 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 21, 'force_embedding': False}
        ],
        22: [
            {'id': 221, 'code': 'srcBb1', 'label': 'source B b 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 22, 'force_embedding': False},
            {'id': 222, 'code': 'srcBb2', 'label': 'source B b 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 22, 'force_embedding': False},
            {'id': 223, 'code': 'srcBb3', 'label': 'source B b 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 22, 'force_embedding': False}
        ],
        23: [
            {'id': 231, 'code': 'srcBc1', 'label': 'source B c 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 23, 'force_embedding': False},
            {'id': 232, 'code': 'srcBc2', 'label': 'source B c 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 23, 'force_embedding': False},
            {'id': 233, 'code': 'srcBc3', 'label': 'source B c 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 23, 'force_embedding': False}
        ],
        31: [
            {'id': 311, 'code': 'srcCa1', 'label': 'source C a 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 31, 'force_embedding': False},
            {'id': 312, 'code': 'srcCa2', 'label': 'source C a 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 31, 'force_embedding': False},
            {'id': 313, 'code': 'srcCa3', 'label': 'source C a 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 31, 'force_embedding': False}
        ],
        32: [
            {'id': 321, 'code': 'srcCb1', 'label': 'source C b 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 32, 'force_embedding': False},
            {'id': 322, 'code': 'srcCb2', 'label': 'source C b 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 32, 'force_embedding': False},
            {'id': 323, 'code': 'srcCb3', 'label': 'source C b 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 32, 'force_embedding': False}
        ],
        33: [
            {'id': 331, 'code': 'srcCc1', 'label': 'source C c 1', 'src_type': 'gcs',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156483,
             'load_interval': 24, 'domain_id': 33, 'force_embedding': False},
            {'id': 332, 'code': 'srcCc2', 'label': 'source C c 2', 'src_type': 'sharepoint',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156583,
             'load_interval': 24, 'domain_id': 33, 'force_embedding': False},
            {'id': 333, 'code': 'srcCc3', 'label': 'source C c 3', 'src_type': 'basic',
             'configuration': json.dumps(CONF_JSON), 'last_load_time': 1726156683,
             'load_interval': 24, 'domain_id': 33, 'force_embedding': False}
        ],
    }

    def __init__(self):
        self.adapters = {}

    def set_test_data(self, mock):
        self.set_perimeters(mock)
        self.set_domains(mock)
        self.set_sources(mock)

    def set_perimeters(self, mock):
        url = '/perimeter/list'
        adapter = mock.get(self.KBOT_BACK_API_URL + url, text=json.dumps(list(self.PERIMETERS_LIST.values())))
        self.adapters[url] = adapter
        for perimeter_id in self.PERIMETERS_LIST:
            perimeter = self.PERIMETERS_LIST[perimeter_id]
            url = '/perimeter/' + str(perimeter.get('id'))
            adapter = mock.get(self.KBOT_BACK_API_URL + url, text=json.dumps(perimeter))
            self.adapters[url] = adapter
            url = '/perimeter/bycode/' + perimeter.get('code')
            adapter = mock.get(self.KBOT_BACK_API_URL + url, text=json.dumps(perimeter))
            self.adapters[url] = adapter

    def set_domains(self, mock):
        for perimeter_id in self.DOMAINS_LIST:
            url = '/perimeter/'+str(perimeter_id)+'/domains'
            adapter = mock.get(self.KBOT_BACK_API_URL + url,
                               text=json.dumps(self.DOMAINS_LIST[perimeter_id]))
            self.adapters[url] = adapter

            for domain in self.DOMAINS_LIST[perimeter_id]:
                url = '/domain/' + str(domain.get('id'))
                adapter = mock.get(self.KBOT_BACK_API_URL + url, text=json.dumps(domain))
                self.adapters[url] = adapter

    def set_sources(self, mock):
        for domain_id in self.SOURCES_LIST:
            url = '/domain/'+str(domain_id)+'/sourcestoload'
            adapter = mock.get(self.KBOT_BACK_API_URL + url,
                               text=json.dumps(self.SOURCES_LIST[domain_id]))
            self.adapters[url] = adapter
            for source in self.SOURCES_LIST[domain_id]:
                url = '/source/' + str(source.get('id'))
                adapter = mock.get(self.KBOT_BACK_API_URL + url, text=json.dumps(source))
                self.adapters[url] = adapter
                url = '/source/' + str(source.get('id')) + '/done'
                adapter = mock.put(self.KBOT_BACK_API_URL + url)
                self.adapters[url] = adapter
