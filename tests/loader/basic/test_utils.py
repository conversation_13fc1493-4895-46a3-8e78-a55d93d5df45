# # tests/test_utils.py

# import os
# import pytest
# import json
# import time
# from unittest import mock
# from unittest.mock import mock_open, patch, MagicMock
# from http import HTTPStatus

# import requests
# from kbotloadscheduler.loader.basic.utils import get_secret, EmptyJSONResponse, failed, retry

# # Test for get_secret function
# def test_get_secret():
#     secret_id = "test_secret"
#     secret_dir_path = "/path/to/secrets"
#     secret_content = "super_secret_value"
#     secret_path = os.path.join(secret_dir_path, secret_id, "secret")

#     m = mock_open(read_data=secret_content)
#     with patch("builtins.open", m):
#         result = get_secret(secret_id, secret_dir_path)
#         m.assert_called_once_with(secret_path, "r")
#         assert result == secret_content

# # Test for EmptyJSONResponse class
# def test_empty_json_response():
#     status_code = 404
#     error_message = "Not Found"
#     response = EmptyJSONResponse(status_code=status_code, error=error_message)

#     assert response.status_code == status_code
#     assert response.error == error_message
#     assert response.json() is None

# # Helper class to test decorators
# class MockClient:
#     def __init__(self):
#         self.failed = []
#         self.headers = {"Authorization": "Bearer old_token"}

#     def get_new_token_headers(self):
#         return {"Authorization": "Bearer new_token"}

# # Test for failed decorator
# def test_failed_decorator():
#     client = MockClient()

#     @failed
#     def mock_request(self, url):
#         response = MagicMock(spec=requests.Response)
#         response.status_code = HTTPStatus.BAD_REQUEST
#         response.error = "Bad Request"
#         return response

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert response.status_code == HTTPStatus.BAD_REQUEST
#     assert len(client.failed) == 1
#     assert client.failed[0] == {
#         "url": url,
#         "status_code": HTTPStatus.BAD_REQUEST,
#         "error": "Bad Request",
#     }

# # Test for retry decorator - successful request on first try
# def test_retry_decorator_success():
#     client = MockClient()

#     @retry(max_retries=3, backoff_factor=1)
#     def mock_request(self, url):
#         response = MagicMock(spec=requests.Response)
#         response.status_code = HTTPStatus.OK
#         return response

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert response.status_code == HTTPStatus.OK

# # Test for retry decorator - retries and succeeds after retries
# def test_retry_decorator_retry_then_success():
#     client = MockClient()

#     @retry(max_retries=3, backoff_factor=0)  # Set backoff_factor=0 for faster tests
#     def mock_request(self, url):
#         if mock_request.attempt < 2:
#             mock_request.attempt += 1
#             response = MagicMock(spec=requests.Response)
#             response.status_code = HTTPStatus.INTERNAL_SERVER_ERROR
#             response.text = "Server Error"
#             response.url = url
#             return response
#         else:
#             response = MagicMock(spec=requests.Response)
#             response.status_code = HTTPStatus.OK
#             return response

#     mock_request.attempt = 0

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert response.status_code == HTTPStatus.OK
#     assert mock_request.attempt == 2

# # Test for retry decorator - max retries exceeded
# def test_retry_decorator_max_retries_exceeded():
#     client = MockClient()

#     @retry(max_retries=2, backoff_factor=0)
#     def mock_request(self, url):
#         response = MagicMock(spec=requests.Response)
#         response.status_code = HTTPStatus.INTERNAL_SERVER_ERROR
#         response.text = "Server Error"
#         response.url = url
#         return response

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert isinstance(response, EmptyJSONResponse)
#     assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
#     assert response.error == str(HTTPStatus.INTERNAL_SERVER_ERROR)

# # Test for retry decorator handling UNAUTHORIZED status
# def test_retry_decorator_handle_unauthorized():
#     client = MockClient()

#     @retry(max_retries=3, backoff_factor=0)
#     def mock_request(self, url):
#         if mock_request.attempt == 0:
#             mock_request.attempt += 1
#             response = MagicMock(spec=requests.Response)
#             response.status_code = HTTPStatus.UNAUTHORIZED
#             response.url = url
#             response.text = "Unauthorized"
#             return response
#         else:
#             response = MagicMock(spec=requests.Response)
#             response.status_code = HTTPStatus.OK
#             return response

#     mock_request.attempt = 0

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert response.status_code == HTTPStatus.OK
#     assert mock_request.attempt == 1
#     assert client.headers["Authorization"] == "Bearer new_token"

# # Test for retry decorator with RequestException
# def test_retry_decorator_with_exception():
#     client = MockClient()

#     @retry(max_retries=2, backoff_factor=0)
#     def mock_request(self, url):
#         raise requests.exceptions.ConnectionError("Connection failed")

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert isinstance(response, EmptyJSONResponse)
#     assert response.status_code == 0
#     assert response.error == "Connection failed"

# # Test for retry decorator with ValueError
# def test_retry_decorator_with_value_error():
#     client = MockClient()

#     @retry(max_retries=2, backoff_factor=0)
#     def mock_request(self, url):
#         raise ValueError("Invalid response")

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"
#     response = decorated_request(url)

#     assert isinstance(response, EmptyJSONResponse)
#     assert response.status_code == 0
#     assert response.error == "None"

# # Test retry decorator's backoff logic (optional - may slow down tests)
# @pytest.mark.skip("Optional test for backoff timing")
# def test_retry_decorator_backoff():
#     client = MockClient()

#     @retry(max_retries=3, backoff_factor=1)
#     def mock_request(self, url):
#         raise requests.exceptions.ConnectionError("Connection failed")

#     decorated_request = mock_request.__get__(client, MockClient)
#     url = "http://example.com/api"

#     start_time = time.time()
#     response = decorated_request(url)
#     end_time = time.time()

#     expected_wait = 1 + 2 + 4  # backoff_factor ** retries
#     actual_wait = end_time - start_time

#     assert actual_wait >= expected_wait
#     assert isinstance(response, EmptyJSONResponse)
#     assert response.status_code == 0

# # Test get_secret with non-existent file
# def test_get_secret_file_not_found():
#     secret_id = "nonexistent_secret"
#     secret_dir_path = "/path/to/secrets"
#     secret_path = os.path.join(secret_dir_path, secret_id, "secret")

#     with patch("builtins.open", mock_open()) as m:
#         m.side_effect = FileNotFoundError
#         with pytest.raises(FileNotFoundError):
#             get_secret(secret_id, secret_dir_path)
#         m.assert_called_once_with(secret_path, "r")

# # Test get_secret with empty file
# def test_get_secret_empty_file():
#     secret_id = "empty_secret"
#     secret_dir_path = "/path/to/secrets"
#     secret_content = ""
#     secret_path = os.path.join(secret_dir_path, secret_id, "secret")

#     m = mock_open(read_data=secret_content)
#     with patch("builtins.open", m):
#         result = get_secret(secret_id, secret_dir_path)
#         m.assert_called_once_with(secret_path, "r")
#         assert result == ""
