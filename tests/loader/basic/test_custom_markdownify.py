import pytest
from kbotloadscheduler.loader.basic.custom_markdownify import (
    clean_markdown, md, CustomMarkdownConverter, handle_unordered_list, html_table_to_markdown, handle_list_item,
    get_alignment, get_cell_text, handle_anchor_tags_in_list
)
from bs4 import BeautifulSoup
import re


# Test cases for clean_markdown function
@pytest.mark.parametrize("input_html, expected_markdown", [
    (
        '<p>This is a <strong>bold</strong> statement.</p>',
        'This is a **bold** statement.'
    ),
    (
        '<p>Mixed <em>styles</em> with <strong>bold</strong> and <a href="url">links</a>.</p>',
        'Mixed styles with **bold** and links.'
    ),
])
def test_clean_markdown(input_html, expected_markdown):
    result = clean_markdown(input_html)
    assert result == expected_markdown


# Test cases for md function with headings
@pytest.mark.parametrize("input_html, expected_markdown", [
    (
        '<h1>Heading 1</h1>',
        '# Heading 1\n\n'
    ),
    (
        '<h2>Heading 2</h2>',
        '## Heading 2\n\n'
    ),
    (
        '<h3>Heading 3</h3>',
        '### Heading 3\n\n'
    ),
    (
        '<h4>Heading 4</h4>',
        '#### Heading 4\n\n'
    ),
    (
        '<h5>Heading 5</h5>',
        '##### Heading 5\n\n'
    ),
])
def test_md_headings(input_html, expected_markdown):
    result = md(input_html)
    assert result == expected_markdown


# Test cases for md function with bold and italic
@pytest.mark.parametrize("input_html, expected_markdown", [
    (
        '<p>This is <strong>bold</strong> text.</p>',
        'This is  **bold**  text.\n\n'
    ),
    (
        '<p>This is <em>italic</em> text.</p>',
        'This is  *italic*  text.\n\n'
    ),
    (
        '<p>Combining <strong>bold</strong> and <em>italic</em> text.</p>',
        'Combining  **bold**  and  *italic*  text.\n\n'
    ),
    (
        '<p><strong>Bold at start</strong> and end <strong>bold</strong>.</p>',
        ' **Bold at start**  and end  **bold** .\n\n'
    ),
])
def test_md_bold_italic(input_html, expected_markdown):
    result = md(input_html)
    assert result == expected_markdown


# Test cases for md function with links
@pytest.mark.parametrize("input_html, expected_markdown", [
    (
        '<p>Visit <a href="https://example.com">Example</a> website.</p>',
        'Visit [Example](https://example.com) website.\n\n'
    ),
    (
        '<p>Empty href <a href="">Link</a>.</p>',
        'Empty href Link.\n\n'
    ),
    (
        '<p>Link without href <a>Just Text</a>.</p>',
        'Link without href Just Text.\n\n'
    ),
    (
        'A nice <a href="/public/content/11428">link</a>',
        'A nice [link](/public/content/11428)'
    )
])
def test_md_links(input_html, expected_markdown):
    result = md(input_html)
    assert result == expected_markdown


@pytest.mark.parametrize("input_html, expected_markdown", [
    (
        '<p>Visit <a href="https://example.com">Example</a> website.</p>',
        'Visit [Example](https://example.com) website.\n\n'
    ),
    (
        '<p>Empty href <a href="">Link</a>.</p>',
        'Empty href Link.\n\n'
    ),
    (
        '<p>Link without href <a>Just Text</a>.</p>',
        'Link without href Just Text.\n\n'
    ),
    (
        'A nice <a href="/public/content/11428">link</a>',
        'A nice [link](/public/content/11428)'
    )
])
def test_md(input_html, expected_markdown):
    result = md(input_html)
    assert result == expected_markdown


class TestCustomMarkdownConverter:
    """
    Test suite for the CustomMarkdownConverter class.
    """

    def setup_method(self):
        """
        Setup method to instantiate the converter before each test.
        """
        self.converter = CustomMarkdownConverter()

    # Tests for Heading Conversions
    @pytest.mark.parametrize("heading_level, html_tag, expected_markdown", [
        (1, 'h1', '# Heading 1\n\n'),
        (2, 'h2', '## Heading 2\n\n'),
        (3, 'h3', '### Heading 3\n\n'),
        (4, 'h4', '#### Heading 4\n\n'),
        (5, 'h5', '##### Heading 5\n\n'),
    ])
    def test_convert_headings(self, heading_level, html_tag, expected_markdown):
        """
        Test that heading tags are correctly converted to Markdown headings.
        """
        html = f'<{html_tag}>Heading {heading_level}</{html_tag}>'
        soup = BeautifulSoup(html, 'html.parser')
        tag = getattr(soup, html_tag)
        convert_method = getattr(self.converter, f'convert_h{heading_level}')
        result = convert_method(tag, f'Heading {heading_level}', False)
        assert result == expected_markdown, f"Failed to convert {html_tag} to Markdown."

    # Tests for Strong and Emphasis Conversions
    @pytest.mark.parametrize("html_tag, text, expected_markdown", [
        ('strong', 'bold text', ' **bold text** '),
        ('em', 'italic text', ' *italic text* '),
    ])
    def test_convert_strong_em(self, html_tag, text, expected_markdown):
        """
        Test that <strong> and <em> tags are correctly converted to Markdown bold and italic syntax.
        """
        html = f'<{html_tag}>{text}</{html_tag}>'
        soup = BeautifulSoup(html, 'html.parser')
        tag = getattr(soup, html_tag)
        convert_method = getattr(self.converter, f'convert_{html_tag}')
        result = convert_method(tag, text, False)
        assert result == expected_markdown, f"Failed to convert <{html_tag}> to Markdown."

    # Tests for Span Conversion
    def test_convert_span(self):
        """
        Test that <span> tags are correctly converted by returning the inner text.
        """
        html = '<span>Simple span text</span>'
        soup = BeautifulSoup(html, 'html.parser')
        span_tag = soup.span
        result = self.converter.convert_span(span_tag, 'Simple span text', False)
        expected = 'Simple span text'
        assert result == expected, "Failed to convert <span> tag."

    # Tests for Table Conversion
    @pytest.mark.parametrize("html_table, expected_markdown", [
        (
            '''
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Age</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Alice</td>
                        <td>30</td>
                    </tr>
                    <tr>
                        <td>Bob</td>
                        <td>25</td>
                    </tr>
                </tbody>
            </table>
            ''',
            '| Name | Age |\n| --- | --- |\n| Alice | 30 |\n| Bob | 25 |\n'
        )
    ])
    def test_convert_table(self, html_table, expected_markdown):
        """
        Test that <table> tags are correctly converted to Markdown tables.
        """
        soup = BeautifulSoup(html_table, 'html.parser')
        table_tag = soup.table
        result = self.converter.convert_table(table_tag, None, None)

        assert result == expected_markdown, "Failed to convert <table> tag."

    # Tests for Unordered List Conversion
    @pytest.mark.parametrize("html_ul, expected_markdown", [
        (
            '''
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>
            ''',
            '- Item 1\n- Item 2\n- Item 3\n'
        ),
        (
            '''
            <ul>
                <li>Item with <strong>bold</strong> text</li>
                <li>Item with <a href="https://example.com">link</a></li>
            </ul>
            ''',
            '- Item with <strong>bold</strong> text\n- Item with [link](https://example.com)\n'
        ),
    ])
    def test_convert_ul(self, html_ul, expected_markdown):
        """
        Test that unordered lists (<ul>) are correctly converted to Markdown.
        """
        soup = BeautifulSoup(html_ul, 'html.parser')
        ul_tag = soup.ul
        result = self.converter.convert_ul(ul_tag, html_ul, False)
        assert result == expected_markdown, "Failed to convert <ul> tag."

    # Tests for Ordered List Conversion
    @pytest.mark.parametrize("html_ol, expected_markdown", [
        (
            '''
            <ol>
                <li>First</li>
                <li>Second</li>
                <li>Third</li>
            </ol>
            ''',
            '1. First\n2. Second\n3. Third\n'
        ),
        (
            '''
            <ol>
                <li>First with <em>italic</em></li>
                <li>Second with <strong>bold</strong></li>
            </ol>
            ''',
            '1. First with <em>italic</em>\n2. Second with <strong>bold</strong>\n'
        ),
    ])
    def test_convert_ol(self, html_ol, expected_markdown):
        """
        Test that ordered lists (<ol>) are correctly converted to numbered Markdown lists.
        """
        soup = BeautifulSoup(html_ol, 'html.parser')
        ol_tag = soup.ol
        result = self.converter.convert_ol(ol_tag, html_ol, False)
        assert result == expected_markdown, "Failed to convert <ol> tag."

    # Tests for List Item Conversion
    @pytest.mark.parametrize("html_li, parent_type, expected_markdown", [
        (
            '<li>List item 1</li>',
            'ul',
            '- List item 1'
        ),
        (
            '<li>List item 2</li>',
            'ol',
            '- List item 2'
        ),
    ])
    def test_convert_li(self, html_li, parent_type, expected_markdown):
        """
        Test that list items (<li>) are correctly converted based on their parent list type.
        """
        soup = BeautifulSoup(html_li, 'html.parser')
        li_tag = soup.find('li')
        result = self.converter.convert_li(li_tag, None, None)
        assert result == expected_markdown, f"Failed to convert <li> tag under <{parent_type}>."

    # Tests for Paragraph Conversion
    @pytest.mark.parametrize("html_p, expected_markdown", [
        (
            '<p>This is a regular paragraph.</p>',
            'This is a regular paragraph.\n\n'
        ),
        (
            '''
            <p>- List-like paragraph item 1</p>
            <p>- List-like paragraph item 2</p>
            ''',
            '- List-like paragraph item 1\n- List-like paragraph item 2\n'
        ),
    ])
    def test_convert_p(self, html_p, expected_markdown):
        """
        Test that paragraphs (<p>) are correctly converted, handling both regular and list-like paragraphs.
        """
        soup = BeautifulSoup(html_p, 'html.parser')
        p_tags = soup.find_all('p')
        markdown = ''
        for p_tag in p_tags:
            result = self.converter.convert_p(p_tag, p_tag.get_text(), False)
            markdown += result
        assert markdown == expected_markdown, "Failed to convert <p> tag."


class TestGetAlignment:
    """
    Test suite for the get_alignment function.
    """

    @pytest.mark.parametrize("cell_html, expected_alignment", [
        # Test Case 1: Align attribute set to left
        (
            '<td align="left">Left Aligned</td>',
            'left'
        ),
        # Test Case 2: Align attribute set to center
        (
            '<td align="center">Center Aligned</td>',
            'center'
        ),
        # Test Case 3: Align attribute set to right
        (
            '<td align="right">Right Aligned</td>',
            'right'
        ),
        # Test Case 4: Style attribute with text-align left
        (
            '<td style="text-align: left;">Left Aligned</td>',
            'left'
        ),
        # Test Case 5: Style attribute with text-align center
        (
            '<td style="text-align: center;">Center Aligned</td>',
            'center'
        ),
        # Test Case 6: Style attribute with text-align right
        (
            '<td style="text-align: right;">Right Aligned</td>',
            'right'
        ),
        # Test Case 7: No alignment specified
        (
            '<td>No Alignment</td>',
            ''
        ),
        # Test Case 8: Mixed attributes, align takes precedence
        (
            '<td align="center" style="text-align: left;">Mixed Alignment</td>',
            'center'
        ),
        # Test Case 9: Invalid alignment value
        (
            '<td align="justify">Justify Alignment</td>',
            'justify'
        ),
        # Test Case 10: Style attribute with multiple styles
        (
            '<td style="color: red; text-align: center;">Styled Center</td>',
            'center'
        ),
    ])
    def test_get_alignment(self, cell_html, expected_alignment):
        """
        Test that get_alignment correctly extracts alignment from <td> or <th> elements.
        """
        soup = BeautifulSoup(cell_html, 'html.parser')
        cell_tag = soup.find(['td', 'th'])
        result = get_alignment(cell_tag)
        assert result == expected_alignment, f"Expected alignment '{expected_alignment}', got '{result}'"


class TestGetCellText:
    """
    Test suite for the get_cell_text function.
    """

    @pytest.mark.parametrize("cell_html, expected_text", [
        # Test Case 1: Simple text
        (
            '<td>Simple Text</td>',
            'Simple Text'
        ),
        # Test Case 2: Text with <br> tags
        (
            '<td>Line 1<br>Line 2</td>',
            'Line 1 Line 2'
        ),
        # Test Case 3: Cell with an image
        (
            '<td><img src="https://example.com/image.png" alt="Example Image"></td>',
            '![Example Image](https://example.com/image.png)'
        ),
        # Test Case 4: Cell with text and image
        (
            '<td>Image: <img src="https://example.com/logo.png" alt="Logo"></td>',
            'Image: ![Logo](https://example.com/logo.png)'
        ),
        # Test Case 5: Cell with multiple images and text
        (
            '<td><img src="https://example.com/img1.png" alt="Img1"> '
            'and <img src="https://example.com/img2.png" alt="Img2"></td>',
            '![Img1](https://example.com/img1.png) and ![Img2](https://example.com/img2.png)'
        ),
        # Test Case 6: Cell with nested tags and text
        (
            '<td><strong>Bold Text</strong> and <em>Italic Text</em></td>',
            'Bold Text and Italic Text'
        ),
        # Test Case 7: Cell with multiple <br> tags
        (
            '<td>First Line<br><br>Second Line</td>',
            'First Line  Second Line'
        ),
        # Test Case 8: Empty cell
        (
            '<td></td>',
            ''
        ),
        # Test Case 9: Cell with only <br> tags
        (
            '<td><br><br></td>',
            ''
        ),
        # Test Case 10: Cell with special characters
        (
            '<td>Special &amp; Characters &lt;Test&gt;</td>',
            'Special & Characters <Test>'
        ),
    ])
    def test_get_cell_text(self, cell_html, expected_text):
        """
        Test that get_cell_text correctly extracts and formats text from table cells.
        """
        soup = BeautifulSoup(cell_html, 'html.parser')
        cell_tag = soup.find(['td', 'th'])
        result = get_cell_text(cell_tag)
        assert result.replace("  ", " ") == expected_text.replace("  ", " "), \
            f"Expected text '{expected_text}', got '{result}'"


class TestHandleAnchorTagsInList:
    """
    Test suite for the handle_anchor_tags_in_list function.
    """

    @pytest.mark.parametrize("li_html, ordered, index, expected_output", [
        # Test Case 1: Unordered list item with a single link
        (
            '<li><a href="https://example.com">Example</a> item</li>',
            False,
            1,
            '[Example](https://example.com) item'
        ),
        # Test Case 2: Ordered list item with a single link
        (
            '<li><a href="https://example.org">Org</a> item</li>',
            True,
            2,
            '2. [Org](https://example.org) item'
        ),
        # Test Case 3: List item with multiple links
        (
            '<li><a href="https://site1.com">Site1</a> and <a href="https://site2.com">Site2</a> items</li>',
            False,
            1,
            '[Site1](https://site1.com) and [Site2](https://site2.com) items'
        ),
        # Test Case 4: List item with link without href
        (
            '<li><a>No Href Link</a> item</li>',
            False,
            1,
            '[No Href Link]() item'
        ),
        # Test Case 5: List item without any links
        (
            '<li>Just a simple item</li>',
            False,
            1,
            'Just a simple item'
        ),
        # Test Case 7: List item with link and special characters
        (
            '<li><a href="https://example.com/page?param=value#section">Complex Link</a> item</li>',
            False,
            1,
            '[Complex Link](https://example.com/page?param=value#section) item'
        ),
        # Test Case 8: List item with nested tags inside link
        (
            '<li><a href="https://example.com"><strong>Bold Link</strong></a> item</li>',
            False,
            1,
            '[Bold Link](https://example.com) item'
        ),
        # Test Case 9: List item with link containing spaces
        (
            '<li><a href="https://example.com/path with spaces">Link with Spaces</a> item</li>',
            False,
            1,
            '[Link with Spaces](https://example.com/path with spaces) item'
        ),
        # Test Case 10: Ordered list item with no link
        (
            '<li>Plain ordered item</li>',
            True,
            3,
            '3. Plain ordered item'
        ),
    ])
    def test_handle_anchor_tags_in_list(self, li_html, ordered, index, expected_output):
        """
        Test that handle_anchor_tags_in_list correctly converts <a> tags to Markdown links within list items.
        """
        soup = BeautifulSoup(li_html, 'html.parser')
        li_tag = soup.find('li')
        result = handle_anchor_tags_in_list(li_tag, ordered=ordered, index=index)

        assert result.replace("\t", "").replace("\n", "") == expected_output.replace("\t", "").replace("\n", ""), \
            f"Expected output '{expected_output}', got '{result}'"


class TestHandleListItem:
    """
    Test suite for the handle_list_item function.
    """

    @pytest.mark.parametrize("li_html, ordered, index, expected_output", [
        # Test Case 1: Unordered list item with plain text
        (
            '<li>Simple item</li>',
            False,
            1,
            'Simple item'
        ),
        # Test Case 2: Ordered list item with plain text
        (
            '<li>First item</li>',
            True,
            1,
            '1. First item'
        ),
        # Test Case 3: List item with <a> tag
        (
            '<li><a href="https://example.com">Link</a> in item</li>',
            False,
            1,
            '[Link](https://example.com) in item'
        ),
        # Test Case 4: List item with multiple formatting
        (
            '<li>Item with <strong>bold</strong> and <em>italic</em> text</li>',
            False,
            1,
            'Item with <strong>bold</strong> and <em>italic</em> text'
        ),
    ])
    def test_handle_list_item(self, li_html, ordered, index, expected_output):
        """
        Test that handle_list_item correctly processes <li> elements with various content.
        """
        soup = BeautifulSoup(li_html, 'html.parser')
        li_tag = soup.find('li')
        result = handle_list_item(li_tag, ordered=ordered, index=index)
        # Replace multiple spaces with single space for consistent comparison
        result = re.sub(r'\s+', ' ', result)
        expected_output = re.sub(r'\s+', ' ', expected_output)

        assert result == expected_output, f"Expected: '{expected_output}'\nGot: '{result}'"

    """
    Test suite for the handle_unordered_list function.
    """
    @pytest.mark.parametrize("html_ul, expected_markdown, list_item_returns", [
        # Test Case 1: Simple unordered list with plain text
        (
            '''
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>
            ''',
            '- Item 1\n- Item 2\n- Item 3\n',
            ['Item 1', 'Item 2', 'Item 3']
        ),
        # Test Case 2: Unordered list with <a> tags
        (
            '''
            <ul>
                <li><a href="https://example.com">Link 1</a></li>
                <li><a href="https://example.org">Link 2</a></li>
            </ul>
            ''',
            '- Link 1\n- Link 2\n',
            ['Link 1', 'Link 2']
        ),
        # Test Case 3: Unordered list with nested lists (should not process nested <li>)
        (
            '''
            <ul>
                <li>Parent Item
                    <ul>
                        <li>Child Item 1</li>
                        <li>Child Item 2</li>
                    </ul>
                </li>
                <li>Another Parent Item</li>
            </ul>
            ''',
            '- Parent Item\n- Another Parent Item\n',
            ['Parent Item', 'Another Parent Item']
        ),
        # Test Case 4: Empty unordered list
        (
            '''
            <ul>
            </ul>
            ''',
            '',
            []
        ),
        # Test Case 5: Unordered list with complex formatting
        (
            '''
            <ul>
                <li>Item with <strong>bold</strong> text</li>
                <li>Item with <em>italic</em> text</li>
                <li>Item with <a href="https://example.com">link</a></li>
            </ul>
            ''',
            '- Item with <strong>bold</strong> text\n'
            '- Item with <em>italic</em> text\n'
            '- Item with [link](https://example.com)\n',
            ['Item with <strong>bold</strong> text',
             'Item with <em>italic</em> text',
             'Item with [link](https://example.com)']
        ),
    ])
    def test_handle_unordered_list(self, mocker, html_ul, expected_markdown, list_item_returns):
        """
        Test that handle_unordered_list correctly converts <ul> elements to Markdown unordered lists.
        """
        # Mock handle_list_item to return predefined values
        mock_handle_list_item = mocker.patch(
            'kbotloadscheduler.loader.basic.custom_markdownify.handle_list_item',
            side_effect=list_item_returns
        )

        # Parse the HTML and find the <ul> tag
        soup = BeautifulSoup(html_ul, 'html.parser')
        ul_tag = soup.ul

        # Call the function under test
        result = handle_unordered_list(ul_tag)

        # Assert that handle_list_item was called the correct number of times with the correct arguments
        assert mock_handle_list_item.call_count == len(list_item_returns), \
            "handle_list_item was not called the expected number of times."

        for li_tag, expected_call in zip(ul_tag.find_all('li', recursive=False), list_item_returns):
            mock_handle_list_item.assert_any_call(li_tag)
        # Assert that the resulting Markdown matches the expected output
        assert result == expected_markdown, f"Expected:\n{expected_markdown}\nGot:\n{result}"


class TestHtmlTableToMarkdown:
    """
    Test suite for the html_table_to_markdown function.
    """

    @pytest.mark.parametrize("html_table, expected_markdown", [
        # Test Case 1: Simple table with headers and rows
        (
            '''
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Age</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Alice</td>
                        <td>30</td>
                    </tr>
                    <tr>
                        <td>Bob</td>
                        <td>25</td>
                    </tr>
                </tbody>
            </table>
            ''',
            '| Name | Age |\n| --- | --- |\n| Alice | 30 |\n| Bob | 25 |\n\n'
        ),
        # Test Case 2: Table with colspan and rowspan
        (
            '''
            <table>
                <tr>
                    <th>Header 1</th>
                    <th>Header 2</th>
                    <th>Header 3</th>
                </tr>
                <tr>
                    <td rowspan="2">Rowspan Cell</td>
                    <td>Cell 1</td>
                    <td>Cell 2</td>
                </tr>
                <tr>
                    <td colspan="2">Colspan Cell</td>
                </tr>
            </table>
            ''',
            '| Header 1 | Header 2 | Header 3 |\n'
            '| --- | --- | --- |\n'
            '| Rowspan Cell | Cell 1 | Cell 2 |\n'
            '| Rowspan Cell | Colspan Cell | Colspan Cell |\n\n'
        ),
        # Test Case 3: Table without thead, first row as header
        (
            '''
            <table>
                <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Available</th>
                </tr>
                <tr>
                    <td>Book</td>
                    <td>$10</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Pen</td>
                    <td>$2</td>
                    <td>No</td>
                </tr>
            </table>
            ''',
            '| Product | Price | Available |\n| --- | --- | --- |\n| Book | $10 | Yes |\n| Pen | $2 | No |\n\n'
        ),
        # Test Case 4: Table with styled headers (alignment)
        (
            '''
            <table>
                <thead>
                    <tr>
                        <th style="text-align: left">Left</th>
                        <th style="text-align: center">Center</th>
                        <th style="text-align: right">Right</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>2</td>
                        <td>3</td>
                    </tr>
                </tbody>
            </table>
            ''',
            '| Left | Center | Right |\n| :--- | :---: | ---: |\n| 1 | 2 | 3 |\n\n'
        ),
        # Test Case 5: Table with images in cells
        (
            '''
            <table>
                <tr>
                    <th>Logo</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td><img src="https://example.com/logo.png" alt="Logo"></td>
                    <td>Company Logo</td>
                </tr>
            </table>
            ''',
            '| Logo | Description |\n| --- | --- |\n| ![Logo](https://example.com/logo.png) | Company Logo |\n\n'
        ),
        # Test Case 6: Empty table
        (
            '''
            <table>
            </table>
            ''',
            ''
        ),
    ])
    def test_html_table_to_markdown(self, html_table, expected_markdown):
        """
        Test that html_table_to_markdown correctly converts HTML tables to Markdown tables.
        """
        result = html_table_to_markdown(html_table)

        print("result:\n", repr(result.strip("\n")))
        print("expected_markdown:\n", repr(expected_markdown.strip("\n")))

        assert result.strip("\n") == expected_markdown.strip("\n"), f"Expected:\n{expected_markdown}\nGot:\n{result}"
