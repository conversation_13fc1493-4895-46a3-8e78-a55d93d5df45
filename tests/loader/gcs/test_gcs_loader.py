from kbotloadscheduler.loader.gcs.gcs_loader import Gcs<PERSON>oader
from kbotloadscheduler.bean.beans import SourceBean, Metadata
from testutils.mock_gcs import MockGcs
from data.gcs_repo_test_data import GcsRepoTestData


class TestGcsLoader:

    def test_get_document_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        GcsRepoTestData.write_get_list(my_mock_gcs)
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        gcs_loader = GcsLoader()
        actual_document_list = gcs_loader.get_document_list(SourceBean(**GcsRepoTestData.SOURCE))

        assert actual_document_list == GcsRepoTestData.get_document_list_for_source()

    def test_get_document(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        document = GcsRepoTestData.get_document_list_for_source()[1]
        output_path = f'{GcsRepoTestData.BASE_OUTPUT}/docs'
        gcs_loader = GcsLoader()
        doc_metadata = gcs_loader.get_document(SourceBean(**GcsRepoTestData.SOURCE),
                                               document,
                                               output_path)

        relative_file_path = 'src1/domainA/diralpha/fileAlpha2.txt'
        assert doc_metadata == {
            Metadata.DOCUMENT_ID: f'{GcsRepoTestData.DOMAINE_CODE}|{GcsRepoTestData.SOURCE_CODE}|{relative_file_path}',
            Metadata.DOCUMENT_NAME: relative_file_path,
            Metadata.SOURCE_URL: f'gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}',
            Metadata.LOCATION: f'gs://{GcsRepoTestData.WORK_BUCKET}/'
                               + f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}',
            Metadata.CREATION_TIME: GcsRepoTestData.DATE_CREATED_A2,
            Metadata.MODIFICATION_TIME: GcsRepoTestData.DATE_A2
        }

        assert my_mock_gcs.return_blob(
            GcsRepoTestData.WORK_BUCKET,
            f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/src1/domainA/diralpha/fileAlpha2.txt'
        ) is not None

    def test_get_documents(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        source_bean = SourceBean(**GcsRepoTestData.SOURCE)
        documents = GcsRepoTestData.get_document_list_for_source()
        output_path = f'{GcsRepoTestData.BASE_OUTPUT}/docs'
        gcs_loader = GcsLoader()
        actual_metadata = []
        for i in range(len(documents)):
            actual_metadata.append(gcs_loader.get_document(source_bean, documents[i], output_path))

        expected_metadata = []
        for doc in documents:
            relative_file_path = doc.name
            expected_metadata.append({
                Metadata.DOCUMENT_ID: doc.id,
                Metadata.DOCUMENT_NAME: doc.name,
                Metadata.SOURCE_URL: f'gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}',
                Metadata.LOCATION: f'gs://{GcsRepoTestData.WORK_BUCKET}/'
                                   + f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}',
                Metadata.CREATION_TIME: GcsRepoTestData.DATE_DICT.get(doc.name).get('creation_time'),
                Metadata.MODIFICATION_TIME: doc.modification_time
            })

        assert actual_metadata == expected_metadata

        for doc in documents:
            assert my_mock_gcs.return_blob(
                GcsRepoTestData.WORK_BUCKET,
                f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc.name}',
                get_blob_call=True
            ) is not None

    def test_get_documents_partial(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        source_bean = SourceBean(**GcsRepoTestData.SOURCE)
        all_documents = GcsRepoTestData.get_document_list_for_source()
        documents = [all_documents[1], all_documents[2]]
        not_documents = [all_documents[0], all_documents[3]]
        output_path = f'{GcsRepoTestData.BASE_OUTPUT}/docs'
        gcs_loader = GcsLoader()
        actual_metadata = []
        for i in range(len(documents)):
            actual_metadata.append(gcs_loader.get_document(source_bean, documents[i], output_path))

        expected_metadata = []
        # On n'utilise que le document d'index 1 et le document d'index 2
        index = 1
        for doc in documents:
            relative_file_path = doc.name
            expected_metadata.append({
                Metadata.DOCUMENT_ID: doc.id,
                Metadata.DOCUMENT_NAME: doc.name,
                Metadata.SOURCE_URL: f'gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}',
                Metadata.LOCATION: f'gs://{GcsRepoTestData.WORK_BUCKET}/'
                                   + f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}',
                Metadata.CREATION_TIME: GcsRepoTestData.DATE_DICT.get(doc.name).get('creation_time'),
                Metadata.MODIFICATION_TIME: doc.modification_time
            })
            index += 1

        assert actual_metadata == expected_metadata

        for doc in documents:
            blob = my_mock_gcs.return_blob(
                GcsRepoTestData.WORK_BUCKET,
                f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc.name}',
                get_blob_call=True
            )
            assert blob is not None

        for doc in not_documents:
            blob = my_mock_gcs.return_blob(
                GcsRepoTestData.WORK_BUCKET,
                f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc.name}',
                get_blob_call=True
            )
            assert blob is None
