# Tests Confluence Adaptés - kbot-load-scheduler

Ce répertoire contient les tests Confluence adaptés depuis le système de tests complet du module Confluence vers l'architecture kbot-load-scheduler.

## 🎯 Objectif

Adapter et intégrer les **477 tests** du système Confluence  avancé pour qu'ils fonctionnent avec l'architecture actuelle de kbot-load-scheduler, tout en conservant MockConfluence existant.

## 🏗️ Architecture des Tests

### **Niveau 1 : Tests d'Intégration Existants** ✅
- **Localisation** : `tests/loader/test_confluence_*.py`
- **Focus** : Intégration avec l'architecture kbot-load-scheduler
- **Outils** : MockConfluence, MockGCS, ConfluenceLoader
- **Status** : Fonctionnels et maintenus

### **Niveau 2 : Tests Avancés Adaptés** 🆕
- **Localisation** : `tests/loader/confluence/` (ce répertoire)
- **Focus** : Tests détaillés des composants Confluence internes
- **Outils** : Suite complète de tests (sécurité, performance, tracking)
- **Status** : En cours d'adaptation

### **Niveau 3 : Tests d'Intégration avec Vraie Instance** ✅
- **Localisation** : `tests/loader/confluence/test_real_confluence_integration.py`
- **Focus** : Tests avec une vraie instance Confluence
- **Outils** : Système de secrets hybride, tests de performance
- **Status** : Complet et fonctionnel
- **Documentation** : [README_INTEGRATION.md](README_INTEGRATION.md)

## 📁 Structure des Tests Adaptés

### Tests Unitaires Adaptés
| Fichier | Description | Status |
|---------|-------------|---------|
| `test_config_adapted.py` | Tests de configuration Confluence | ✅ Adapté |
| `test_auth_adapted.py` | Tests d'authentification | ✅ Adapté |
| `test_models_adapted.py` | Tests des modèles de données | ✅ Adapté |
| `test_security_adapted.py` | Tests de sécurité | 🔄 En cours |
| `test_tracking_adapted.py` | Tests de tracking | 🔄 En cours |

### Tests de Performance (À adapter)
| Fichier Original | Fichier Adapté | Status |
|------------------|----------------|---------|
| `test_optimized_client.py` | `test_performance_adapted.py` | 📋 Planifié |
| `test_parallel_pagination.py` | `test_pagination_adapted.py` | 📋 Planifié |

### Tests d'Intégration (À adapter)
| Fichier Original | Fichier Adapté | Status |
|------------------|----------------|---------|
| `test_integration_complete.py` | `test_integration_adapted.py` | 📋 Planifié |
| `test_health_checks_integration.py` | `test_health_adapted.py` | 📋 Planifié |

### Tests d'Intégration avec Vraie Instance ✅
| Fichier | Description | Status |
|---------|-------------|---------|
| `test_real_confluence_integration.py` | Tests avec vraie instance Confluence | ✅ Complet |
| `test_performance_integration.py` | Tests de performance avec vraie instance | ✅ Complet |
| `run_integration_tests.py` | Script utilitaire pour tests d'intégration | ✅ Complet |
| `demo_integration.py` | Démonstration guidée | ✅ Complet |
| `README_INTEGRATION.md` | Documentation complète | ✅ Complet |

## 🚀 Lancement des Tests

### Tests Unitaires Adaptés
```bash
# Tests de base (unitaires adaptés)
python tests/loader/confluence/run_adapted_tests.py

# Tests unitaires uniquement
python tests/loader/confluence/run_adapted_tests.py --unit

# Tests d'intégration (existants + adaptés)
python tests/loader/confluence/run_adapted_tests.py --integration

# Tests de sécurité
python tests/loader/confluence/run_adapted_tests.py --security

# Tous les tests avec couverture
python tests/loader/confluence/run_adapted_tests.py --all --coverage
```

### Tests d'Intégration avec Vraie Instance ✅
```bash
# Configuration des secrets (première fois)
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Tests rapides d'intégration
cd tests/loader/confluence
python run_integration_tests.py --run-fast

# Tests de performance
python run_integration_tests.py --run-performance

# Démonstration guidée
python demo_integration.py

# Documentation complète
# Voir README_INTEGRATION.md
```

### Tests Individuels
```bash
# Test de configuration adapté
pytest tests/loader/confluence/test_config_adapted.py -v

# Test d'authentification adapté
pytest tests/loader/confluence/test_auth_adapted.py -v

# Test de modèles adapté
pytest tests/loader/confluence/test_models_adapted.py -v

# Tous les tests adaptés
pytest tests/loader/confluence/ -v
```

### Tests avec Markers
```bash
# Tests unitaires uniquement
pytest -m "unit and confluence" tests/loader/confluence/ -v

# Tests de sécurité
pytest -m "security and confluence" tests/loader/confluence/ -v

# Tests rapides (non lents)
pytest -m "unit and not slow" tests/loader/confluence/ -v
```

## 🔧 Configuration

### Variables d'Environnement
```bash
# Configuration de base pour les tests
export CONFLUENCE_URL=https://test.atlassian.net
export DEFAULT_SPACE_KEY=TEST
export CONFLUENCE_TIMEOUT=30
```

### Markers Pytest
Les tests utilisent les markers suivants :
- `@pytest.mark.unit` : Tests unitaires rapides
- `@pytest.mark.confluence` : Tests spécifiques à Confluence
- `@pytest.mark.security` : Tests de sécurité
- `@pytest.mark.performance` : Tests de performance
- `@pytest.mark.integration` : Tests d'intégration

## 📊 Progression de l'Adaptation

### ✅ Complété
- [x] Configuration pytest avec markers
- [x] Tests de configuration adaptés
- [x] Tests d'authentification adaptés
- [x] Tests de modèles adaptés
- [x] Script de lancement des tests
- [x] Documentation

### 🔄 En Cours
- [ ] Tests de sécurité adaptés
- [ ] Tests de tracking adaptés
- [ ] Tests de performance adaptés

### 📋 Planifié
- [ ] Tests d'intégration avancés
- [ ] Tests de health checks
- [ ] Tests de pagination
- [ ] Tests de circuit breaker
- [ ] Tests de storage

## 🎯 Objectifs de Couverture

- **Tests adaptés** : >90% de couverture
- **Intégration** : Compatibilité avec MockConfluence existant
- **Performance** : Tests rapides (<5s pour les unitaires)
- **Sécurité** : Validation complète des credentials et logs

## 🔍 Différences avec les Tests Originaux

### Adaptations Principales
1. **Imports** : `confluence_rag` → `kbotloadscheduler.loader.confluence`
2. **Architecture** : Adaptation aux beans et services existants
3. **MockConfluence** : Réutilisation du mock existant (excellent)
4. **Configuration** : Intégration avec le système de configuration actuel

### Fonctionnalités Conservées
- ✅ Tous les patterns de test
- ✅ Markers pytest
- ✅ Structure des assertions
- ✅ Logique de validation
- ✅ Gestion des erreurs

## 🛠️ Développement

### Ajouter un Nouveau Test Adapté
1. Copier le test original depuis `src/kbotloadscheduler/loader/confluence/tests/`
2. Adapter les imports : `confluence_rag` → `kbotloadscheduler.loader.confluence`
3. Ajouter les markers appropriés
4. Tester avec `pytest tests/loader/confluence/test_nouveau.py -v`

### Exemple d'Adaptation
```python
# Original
from confluence_rag.config import ConfluenceConfig

# Adapté
from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
```

## 📞 Support

Pour toute question sur l'adaptation des tests :
1. Consulter les tests existants dans `tests/loader/test_confluence_*.py`
2. Vérifier la documentation dans `GUIDE_TESTS_CONFLUENCE.md`
3. Examiner les tests originaux dans `src/kbotloadscheduler/loader/confluence/tests/`
