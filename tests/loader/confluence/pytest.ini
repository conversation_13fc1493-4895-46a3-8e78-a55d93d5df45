[tool:pytest]
# Configuration pytest pour les tests d'intégration Confluence

# Marqueurs personnalisés
markers =
    integration: Tests d'intégration avec vraie instance Confluence
    performance: Tests de performance avec vraie instance Confluence
    slow: Tests lents (> 30 secondes)
    unit: Tests unitaires avec mocks

# Options par défaut
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings

# Répertoires de tests
testpaths = 
    test_real_confluence_integration.py
    test_performance_integration.py

# Filtres d'avertissements
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:google.*

# Timeout pour les tests (en secondes)
timeout = 300

# Variables d'environnement pour les tests
env =
    PYTHONPATH = ../../../
    PYTEST_CURRENT_TEST = {envname}
