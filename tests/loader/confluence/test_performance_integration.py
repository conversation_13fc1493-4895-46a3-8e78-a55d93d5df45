#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance avec une vraie instance Confluence.
Ces tests mesurent les temps de réponse et les performances du loader.

Usage:
    pytest tests/loader/confluence/test_performance_integration.py -v -m performance
"""

import os
import sys
import pytest
import json
import time
import statistics
from typing import List, Dict, Any
from dotenv import load_dotenv

# Ajouter le chemin du projet au PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from kbotloadscheduler.bean.beans import SourceBean
from tests.loader.confluence.test_real_confluence_integration import (
    check_confluence_config, 
    confluence_config,
    real_config_with_secret,
    real_env_vars,
    real_container
)

# Charger les variables d'environnement
load_dotenv()


def pytest_configure(config):
    """Configuration pytest pour marquer les tests de performance."""
    config.addinivalue_line(
        "markers", "performance: marque les tests de performance avec vraie instance Confluence"
    )


class PerformanceMetrics:
    """Classe pour collecter et analyser les métriques de performance."""
    
    def __init__(self):
        self.measurements: List[Dict[str, Any]] = []
    
    def add_measurement(self, operation: str, duration: float, **kwargs):
        """Ajoute une mesure de performance."""
        self.measurements.append({
            "operation": operation,
            "duration": duration,
            "timestamp": time.time(),
            **kwargs
        })
    
    def get_stats(self, operation: str = None) -> Dict[str, float]:
        """Calcule les statistiques pour une opération."""
        if operation:
            durations = [m["duration"] for m in self.measurements if m["operation"] == operation]
        else:
            durations = [m["duration"] for m in self.measurements]
        
        if not durations:
            return {}
        
        return {
            "count": len(durations),
            "min": min(durations),
            "max": max(durations),
            "mean": statistics.mean(durations),
            "median": statistics.median(durations),
            "stdev": statistics.stdev(durations) if len(durations) > 1 else 0
        }
    
    def print_report(self):
        """Affiche un rapport de performance."""
        operations = set(m["operation"] for m in self.measurements)
        
        print("\n📊 RAPPORT DE PERFORMANCE")
        print("=" * 50)
        
        for operation in sorted(operations):
            stats = self.get_stats(operation)
            if stats:
                print(f"\n🔍 {operation}")
                print(f"   Nombre d'exécutions: {stats['count']}")
                print(f"   Temps moyen: {stats['mean']:.2f}s")
                print(f"   Temps médian: {stats['median']:.2f}s")
                print(f"   Min/Max: {stats['min']:.2f}s / {stats['max']:.2f}s")
                if stats['stdev'] > 0:
                    print(f"   Écart-type: {stats['stdev']:.2f}s")


@pytest.fixture
def performance_metrics():
    """Fixture pour collecter les métriques de performance."""
    metrics = PerformanceMetrics()
    yield metrics
    metrics.print_report()


@pytest.fixture
def performance_source(confluence_config):
    """Source optimisée pour les tests de performance."""
    source_config = {
        "spaces": [confluence_config["test_space"]],
        "max_results": 20,  # Plus de résultats pour tester la performance
        "include_attachments": False,  # Désactiver pour les tests de base
        "content_types": ["page"],
        "last_modified_days": 365
    }
    
    return SourceBean(
        id=1,
        code="perf_test_confluence",
        label="Performance Test Confluence",
        src_type="confluence",
        configuration=json.dumps(source_config),
        last_load_time=0,
        load_interval=24,
        domain_code="perf-domain",
        perimeter_code="perf-perimeter"
    )


@pytest.mark.performance
class TestConfluencePerformance:
    """Tests de performance pour le loader Confluence."""
    
    def test_document_list_performance(self, real_container, performance_source, real_env_vars, performance_metrics):
        """Teste les performances de récupération de la liste des documents."""
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")
        
        # Mesurer plusieurs exécutions
        iterations = 3
        for i in range(iterations):
            start_time = time.time()
            documents = confluence_loader.get_document_list(performance_source)
            duration = time.time() - start_time
            
            performance_metrics.add_measurement(
                "get_document_list",
                duration,
                iteration=i+1,
                document_count=len(documents)
            )
            
            print(f"Itération {i+1}: {len(documents)} documents en {duration:.2f}s")
        
        # Vérifications de performance
        stats = performance_metrics.get_stats("get_document_list")
        assert stats["mean"] < 30.0, f"Temps moyen trop élevé: {stats['mean']:.2f}s"
        assert stats["max"] < 60.0, f"Temps maximum trop élevé: {stats['max']:.2f}s"
    
    def test_connection_performance(self, real_container, confluence_config, real_env_vars, performance_metrics):
        """Teste les performances de connexion."""
        # Mesurer le temps d'initialisation du loader
        iterations = 5
        
        for i in range(iterations):
            start_time = time.time()
            loader_manager = real_container.loader_manager()
            confluence_loader = loader_manager.get_loader("confluence")
            duration = time.time() - start_time
            
            performance_metrics.add_measurement(
                "loader_initialization",
                duration,
                iteration=i+1
            )
        
        stats = performance_metrics.get_stats("loader_initialization")
        assert stats["mean"] < 5.0, f"Initialisation trop lente: {stats['mean']:.2f}s"
    
    @pytest.mark.slow
    def test_large_result_set_performance(self, real_container, confluence_config, real_env_vars, performance_metrics):
        """Teste les performances avec un grand nombre de résultats."""
        # Source avec plus de résultats
        large_source_config = {
            "spaces": [confluence_config["test_space"]],
            "max_results": 100,  # Plus de résultats
            "include_attachments": False,
            "content_types": ["page", "blogpost"],
            "last_modified_days": 365
        }
        
        large_source = SourceBean(
            id=2,
            code="large_perf_test",
            label="Large Performance Test",
            src_type="confluence",
            configuration=json.dumps(large_source_config),
            last_load_time=0,
            load_interval=24,
            domain_code="large-domain",
            perimeter_code="large-perimeter"
        )
        
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")
        
        start_time = time.time()
        documents = confluence_loader.get_document_list(large_source)
        duration = time.time() - start_time
        
        performance_metrics.add_measurement(
            "large_document_list",
            duration,
            document_count=len(documents),
            max_results=100
        )
        
        print(f"Récupération de {len(documents)} documents en {duration:.2f}s")
        
        # Vérifications
        if len(documents) > 0:
            throughput = len(documents) / duration
            print(f"Débit: {throughput:.1f} documents/seconde")
            assert throughput > 1.0, f"Débit trop faible: {throughput:.1f} docs/s"
    
    def test_memory_usage_stability(self, real_container, performance_source, real_env_vars, performance_metrics):
        """Teste la stabilité de l'utilisation mémoire."""
        import psutil
        import gc
        
        process = psutil.Process()
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")
        
        # Mesure initiale
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Exécuter plusieurs fois la récupération
        for i in range(5):
            start_time = time.time()
            documents = confluence_loader.get_document_list(performance_source)
            duration = time.time() - start_time
            
            # Forcer le garbage collection
            gc.collect()
            
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            performance_metrics.add_measurement(
                "memory_usage",
                duration,
                iteration=i+1,
                memory_mb=current_memory,
                memory_increase_mb=memory_increase,
                document_count=len(documents)
            )
            
            print(f"Itération {i+1}: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
        
        # Vérifier que l'augmentation mémoire reste raisonnable
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        assert total_increase < 100, f"Augmentation mémoire excessive: {total_increase:.1f}MB"
        print(f"Augmentation mémoire totale: {total_increase:.1f}MB")


if __name__ == "__main__":
    # Exécuter les tests de performance
    pytest.main([
        "-xvs", 
        __file__, 
        "-m", "performance",
        "--tb=short"
    ])
