#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance adaptés pour le loader Confluence.

Adapté depuis src/kbotloadscheduler/loader/confluence/tests/
pour l'architecture kbot-load-scheduler.
"""

import asyncio
import time
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from kbotloadscheduler.loader.confluence.config import ConfluenceConfig, SearchCriteria


class TestConfluencePerformanceAdapted(unittest.TestCase):
    """Tests de performance adaptés pour le loader Confluence."""

    def setUp(self):
        """Configuration des tests."""
        self.base_config_data = {
            "url": "https://test.atlassian.net",
            "default_space_key": "TEST",
            "pat_token": "test_pat_token"
        }

    @pytest.mark.performance
    @pytest.mark.confluence
    def test_config_creation_performance(self):
        """Test de performance de création de configuration."""
        start_time = time.time()
        
        # Créer plusieurs configurations
        configs = []
        for i in range(100):
            config = ConfluenceConfig(
                **self.base_config_data,
                timeout=30 + i % 10  # Varier le timeout
            )
            configs.append(config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Vérifier que toutes les configurations sont créées
        assert len(configs) == 100
        assert all(config.url.host == "test.atlassian.net" for config in configs)
        
        # Vérifier que la création est rapide (moins de 1 seconde pour 100 configs)
        assert execution_time < 1.0, f"Création trop lente: {execution_time:.4f}s"

    @pytest.mark.performance
    @pytest.mark.confluence
    def test_search_criteria_creation_performance(self):
        """Test de performance de création de critères de recherche."""
        start_time = time.time()
        
        # Créer plusieurs critères de recherche
        criteria_list = []
        for i in range(1000):
            criteria = SearchCriteria(
                spaces=[f"SPACE{i % 10}"],
                content_types=["page", "blogpost"],
                max_results=50 + i % 50
            )
            criteria_list.append(criteria)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Vérifier que tous les critères sont créés
        assert len(criteria_list) == 1000
        assert all(len(criteria.spaces) == 1 for criteria in criteria_list)
        
        # Vérifier que la création est rapide (moins de 0.5 seconde pour 1000 critères)
        assert execution_time < 0.5, f"Création trop lente: {execution_time:.4f}s"

    @pytest.mark.performance
    @pytest.mark.confluence
    @pytest.mark.slow
    def test_config_validation_performance(self):
        """Test de performance de validation de configuration."""
        start_time = time.time()
        
        # Tester la validation avec différentes configurations
        valid_configs = 0
        invalid_configs = 0
        
        for i in range(100):
            try:
                if i % 10 == 0:
                    # Configuration invalide (URL manquante)
                    ConfluenceConfig(
                        default_space_key="TEST",
                        pat_token="test_token"
                    )
                else:
                    # Configuration valide
                    ConfluenceConfig(
                        url=f"https://test{i}.atlassian.net",
                        default_space_key=f"TEST{i}",
                        pat_token="test_token"
                    )
                valid_configs += 1
            except Exception:
                invalid_configs += 1
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Vérifier que la validation fonctionne
        assert valid_configs == 90  # 90 configurations valides
        assert invalid_configs == 10  # 10 configurations invalides
        
        # Vérifier que la validation est rapide
        assert execution_time < 2.0, f"Validation trop lente: {execution_time:.4f}s"

    @pytest.mark.performance
    @pytest.mark.confluence
    def test_memory_usage_config_creation(self):
        """Test de l'utilisation mémoire lors de la création de configurations."""
        import gc
        import sys
        
        # Forcer le garbage collection
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # Créer et détruire des configurations
        configs = []
        for i in range(50):
            config = ConfluenceConfig(**self.base_config_data)
            configs.append(config)
        
        # Supprimer les références
        del configs
        gc.collect()
        
        final_objects = len(gc.get_objects())
        
        # Vérifier que la fuite mémoire est minimale
        # (tolérance de 100 objets pour les structures internes de Python)
        object_increase = final_objects - initial_objects
        assert object_increase < 100, f"Possible fuite mémoire: {object_increase} objets"

    @pytest.mark.performance
    @pytest.mark.confluence
    @pytest.mark.slow
    def test_concurrent_config_access(self):
        """Test de performance d'accès concurrent aux configurations."""
        import threading
        import queue
        
        config = ConfluenceConfig(**self.base_config_data)
        results = queue.Queue()
        
        def access_config():
            """Fonction d'accès à la configuration."""
            start = time.time()
            for _ in range(100):
                # Accéder aux propriétés de la configuration
                _ = config.url
                _ = config.default_space_key
                _ = config.timeout
            end = time.time()
            results.put(end - start)
        
        # Créer plusieurs threads
        threads = []
        start_time = time.time()
        
        for _ in range(10):
            thread = threading.Thread(target=access_config)
            threads.append(thread)
            thread.start()
        
        # Attendre que tous les threads se terminent
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # Vérifier que tous les threads ont terminé
        assert results.qsize() == 10
        
        # Vérifier que l'accès concurrent est efficace
        assert total_time < 2.0, f"Accès concurrent trop lent: {total_time:.4f}s"
        
        # Vérifier que chaque thread a été rapide
        while not results.empty():
            thread_time = results.get()
            assert thread_time < 1.0, f"Thread individuel trop lent: {thread_time:.4f}s"


if __name__ == '__main__':
    unittest.main()
