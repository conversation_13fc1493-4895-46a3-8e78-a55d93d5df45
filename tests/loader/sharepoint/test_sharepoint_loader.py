# test_basic_loader.py
from unittest.mock import MagicMock

from dependency_injector import providers
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from data.sharepoint_repo_test_data import SharepointRepoTestData
import json
import re
from datetime import datetime

from src.kbotloadscheduler.loader.sharepoint import sharepoint_loader
from src.kbotloadscheduler.loader.sharepoint.sharepoint_loader import SharepointLoader
from src.kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from tests.testutils.mock_gcs import MockGcs


class TestSharepointLoader:

    def test_get_single_subfolder(self, mocker, requests_mock):
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativeUrl", "")+'.*\\)/files\\?\\$select')
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_SINGLE_SUBFOLDER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativeUrl", "")+'.*\\)/folders\\?\\$select')
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_FOLDERS)

        folder_infos = SharepointRepoTestData.SINGLE_FOLDER_INFO_INPUT

        document_list = loader.get_files_and_subfolders(sharepoint_client, folder_infos)
        assert "Benchmark Excellence _ 230417.pdf" == document_list[0]['Name']
        assert 1 == len(document_list)

    def test_get_document_list(self, mocker, requests_mock):
        config_with_secret = self.__init_config__(mocker)

        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        root_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativeUrl", "")+'.*\\)\\?\\$select')
        requests_mock.get(root_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativeUrl", "")+'.*\\)/files\\?\\$select')
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativeUrl", "")+'.*\\)/folders\\?\\$select')
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_FOLDERS)
        # Call get_document_list
        actual_document_list = loader.get_document_list(source)
        # Assertions
        assert SharepointRepoTestData.FIRST_DOCUMENT_IN_DOCUMENT_LIST_ID == actual_document_list[0].id
        assert 63 == len(actual_document_list)

    def test_get_document(self, mocker, requests_mock):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativeUrl", "")+'.*\\)\\?\\$select')
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativeUrl", "")+'.*\\)/\\$value')
        requests_mock.get(file_get_content,
                          text="fake content")
        document_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/fichier.pdf',
            name='domA/srcA1/chemin/fichier.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        loader.get_document(source, document_to_load, "gs://fakebucket/sharepointdoc/")
        assert my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True
        ) is not None
        assert b"fake content" == my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True).download_as_string()

    def test_get_documents(self, mocker, requests_mock):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativeUrl", "") + '.*\\)\\?\\$select')
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativeUrl", "") + '.*\\)/\\$value')
        requests_mock.get(file_get_content,
                          text="fake content")

        document1_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/doc1.pdf',
            name='domA/srcA1/chemin/doc1.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 8, 12, 12, 42, 36))

        document2_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/doc2.pdf',
            name='domA/srcA1/chemin/doc2.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        metadatas = [
            loader.get_document(source, document1_to_load, "gs://fakebucket/sharepointdoc/"),
            loader.get_document(source, document2_to_load, "gs://fakebucket/sharepointdoc/")
        ]
        assert ('domA|srcA1|domA/srcA1/chemin/doc1.pdf' == metadatas[0]['document_id'])
        assert ('domA|srcA1|domA/srcA1/chemin/doc2.pdf' == metadatas[1]['document_id'])

    def __init_config__(self, mocker):
        config = providers.Configuration()
        config.env.from_env('ENV', 'local')
        config.gcp_project_id.from_env('GCP_PROJECT_ID', '')
        config_with_secret = ConfigWithSecret(config=config)
        config_with_secret.get_sharepoint_client_config = MagicMock(
            return_value=json.loads(SharepointRepoTestData.FAKE_CLIENT_CONFIG))
        config_with_secret.get_sharepoint_client_private_key = MagicMock(return_value="private_key")

        def get_token(obj):
            return "fake_token"

        mocker.patch.object(
            sharepoint_loader.SharepointCredentials, "get_access_token", get_token)

        return config_with_secret
