import json
from datetime import datetime, timezone
from kbotloadscheduler.loader.sharepoint.sharepoint_loader import SharepointLoader
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from testutils.mock_gcs import MockGcs

# L'objectif de cette classe est de tester une nouvelle configuration sharepoint.
#
# Dans conf/tests/PERIMETER_CODE-sharepoint-client-config/secret mettre le json client config
# Dans conf/tests/PERIMETER_CODE-sharepoint-client-private-key/secret mettre la private key protégée
#
# Ci-dessous remplir :
#  - PERIMETER_CODE : le code du périmètre (correspond au nom de fichier)
#  - SITE_NAME : le nom du site sharepoint
#  - DIRECTORY : le répertoire sous lequel lister les documents
#  - DOCUMENT : le nom d'un fichier à récupérer dans le répertoire ci-dessus
#
# Préfixer les méthodes get_document_list et get_document par test_
# pour activer les tests.
#
# Jouer le test test_get_document_list : il échoue mais cela vous permet de vérifier
# si la liste des documents a bien été retournée
#
# Jouer le test test_get_document : il échoue mais cela vous permet de vérifier les metadata retournées.
# Il faut en particulier vérifier le champs locattion : s'il est rempli c'est qu'on a bien récupéré le document
#
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# !!! ATTENTION !!! Ne jamais mettre vos modifs de ce fichier ou le secrets dans gitlab !!!
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
#


class TestSharepointLoaderNewConfig:
    PERIMETER_CODE = "sandbox"
    SITE_NAME = "KnowledgeBot"
    DIRECTORY = 'Documents partages/Instance POC/Docs IA'
    DOCUMENT = 'Apprendre à jouer à Minecraft.pdf'
    CONF_SOURCE = {
        'site_name': SITE_NAME,
        'relative_directory': DIRECTORY,
    }
    SOURCE = {
        'id': 1, 'code': f'src_{PERIMETER_CODE}', 'label': f'source {PERIMETER_CODE}', 'src_type': 'sharepoint',
        'configuration': json.dumps(CONF_SOURCE), 'last_load_time': 1726156483, 'next_load_time': 1726156483,
        'load_interval': '24', 'domain_code': f'dom_{PERIMETER_CODE}', 'perimeter_code': PERIMETER_CODE
    }

    def get_document_list(self, client, mocker):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        actual_document_list = sharepoint_loader.get_document_list(SourceBean(**self.SOURCE))

        assert actual_document_list == {}

    def get_document(self, client, mocker):
        output_path = "gs://document_tmp-dev/shptests"
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('gs://document_tmp-dev')
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        file_name = self.DOCUMENT
        file_relative_url = f"/sites/{site_name}/{dir_path}/{file_name}"
        document = DocumentBean(
            id=f"domA|srcA1|{file_relative_url}",
            path=f"https://orange0.sharepoint.com{file_relative_url}",
            name=file_relative_url,
            modification_time=datetime(2024, 1, 2, 12, 46, 52, tzinfo=timezone.utc)
        )
        doc_metadata = sharepoint_loader.get_document(
            SourceBean(**self.SOURCE), document, output_path)

        assert doc_metadata == {}
