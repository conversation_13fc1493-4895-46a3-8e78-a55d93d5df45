import pytest
import sys
import os
import logging
from fastapi.testclient import TestClient
import testutils.testutils as tu


@pytest.fixture(scope="session", autouse=True)
def resources():
    return tu.find_resources()


@pytest.fixture(scope="session", autouse=True)
def initlog():
    root = logging.getLogger()
    root.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    root.addHandler(handler)
    return root


@pytest.fixture(scope="session", autouse=True)
def client():
    conf_path = find_conf_directory()
    print(conf_path)
    os.environ['ENV'] = 'tests'
    os.environ['GCP_PROJECT_ID'] = 'ofr-ekb-knowledgebot-test'
    os.environ['ROOT_TEST'] = conf_path.replace('/conf', '/tests')
    os.environ['URL_SERVICE_SHAREPOINT'] = "https://orange0.sharepoint.com/"
    os.environ['PATH_TO_SECRET_CONFIG'] = f'{conf_path}/etc/secrets/tests'
    os.environ['URL_SERVICE_BASIC'] = "https://mockservice.com"
    os.environ['OKAPI_URL_BASIC'] = "https://mockokapi.com"
    os.environ['SERVICE_SCOPE_BASIC'] = "basic_scope"
    os.environ['TIMEOUT_BASIC'] = "30"
    from kbotloadscheduler.main import app
    yield TestClient(app)


def find_conf_directory():
    path = "conf"
    abs_path = os.path.abspath(path)
    while not os.path.exists(abs_path):
        path = '../' + path
        abs_path = os.path.abspath(path)
        if abs_path == '/conf':
            raise Exception('conf directory not found')
    return abs_path
