import json
from kbotloadscheduler.bean.beans import SourceBean
from data.kbot_back_api_test_data import KbotBackApiTestData
from tests.testutils.mock_gcs import MockGcs
from tests.testutils.mock_datetime import MockDatetime


class TestSourcesRoutes:

    def test_load_sources(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('mon_bucket-perimA')
        my_mock_gcs.add_bucket('mon_bucket-perimB')
        my_mock_gcs.add_bucket('mon_bucket-perimC')
        MockDatetime(mocker, "kbotloadscheduler.service.sources_service.datetime", "202409121430")

        response = client.post('/sources/load/perimB')
        assert response.status_code == 200
        actual_sources = response.json()

        expected_sources = []
        expected_sources.extend([self.source_bean_dict(s, 'domBa', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([self.source_bean_dict(s, 'domBb', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([self.source_bean_dict(s, 'domBc', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])

        assert actual_sources == expected_sources

        for expected_source in expected_sources:
            expected_source_id = expected_source.get('id')
            adapter = kbot_back_api_test_data.adapters.get(f'/source/{expected_source_id}/done')
            assert adapter.called
            assert adapter.call_count == 1

            blob = my_mock_gcs.return_blob(
                'mon_bucket-' + expected_source.get('perimeter_code'),
                '202409121430/' + expected_source.get('domain_code') + '/'+expected_source.get('code')
                + '/getlist/getlist.json')
            actual_source = SourceBean(**json.loads(blob.download_as_string()))
            assert actual_source == SourceBean(**expected_source)

    def test_load_all_sources(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('mon_bucket-perimA')
        my_mock_gcs.add_bucket('mon_bucket-perimB')
        my_mock_gcs.add_bucket('mon_bucket-perimC')
        MockDatetime(mocker, "kbotloadscheduler.service.sources_service.datetime", "202409121630")

        response = client.post('/sources/loadall')
        assert response.status_code == 200
        actual_sources = response.json()

        expected_sources = []
        expected_sources.extend([self.source_bean_dict(s, 'domAa', 'perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[11]])
        expected_sources.extend([self.source_bean_dict(s, 'domAb', 'perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[12]])
        expected_sources.extend([self.source_bean_dict(s, 'domAc', 'perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[13]])
        expected_sources.extend([self.source_bean_dict(s, 'domBa', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([self.source_bean_dict(s, 'domBb', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([self.source_bean_dict(s, 'domBc', 'perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])
        expected_sources.extend([self.source_bean_dict(s, 'domCa', 'perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[31]])
        expected_sources.extend([self.source_bean_dict(s, 'domCb', 'perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[32]])
        expected_sources.extend([self.source_bean_dict(s, 'domCc', 'perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[33]])

        assert actual_sources == expected_sources

        for expected_source in expected_sources:
            expected_source_id = expected_source.get('id')
            adapter = kbot_back_api_test_data.adapters.get(f'/source/{expected_source_id}/done')
            assert adapter.called
            assert adapter.call_count == 1

            blob = my_mock_gcs.return_blob(
                'mon_bucket-' + expected_source.get('perimeter_code'),
                '202409121630/' + expected_source.get('domain_code') + '/'+expected_source.get('code')
                + '/getlist/getlist.json')
            actual_source = SourceBean(**json.loads(blob.download_as_string()))
            assert actual_source == SourceBean(**expected_source)

    @staticmethod
    def source_bean_dict(source, domain_code, perimeter_code):
        s = {**source, 'domain_code': domain_code, 'perimeter_code': perimeter_code}
        # domain_id is in the kbot-back api response but is not returned in kbt-load-scheduler response
        s.pop('domain_id', None)
        return s
