from datetime import datetime


class MockDatetime:
    def __init__(self, mocker, datetime_path, str_now):
        self.mocker = mocker
        # datetime_path is the path to the module used inside the tested class
        # from datatime import datatime
        self.datetime_path = datetime_path
        self.now = str_now

        self.my_datatime_mock = mocker.patch(datetime_path)

        def now(tz):
            return datetime.strptime(self.now, '%Y%m%d%H%M')

        mocker.patch.object(self.my_datatime_mock, "now", now)
