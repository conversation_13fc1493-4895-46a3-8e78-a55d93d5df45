# kbot-load-scheduler

![](https://img.shields.io/badge/version-0.17.3-blue.svg)

![](https://img.shields.io/badge/python-3.9+-green.svg)

![](https://img.shields.io/badge/license-Proprietary-orange.svg)

`kbot-load-scheduler` est un composant central de l'écosystème Knowledge Bot, conçu pour orchestrer et automatiser le chargement, la synchronisation et la préparation des données provenant de diverses sources de connaissance. Son objectif est de garantir que le Knowledge Bot dispose toujours d'informations à jour et pertinentes, en gérant de manière robuste et efficace le cycle de vie des documents.

Ce système vise à :

- **Automatiser** le processus de mise à jour des connaissances.
- Assurer un **chargement fiable et résilient** des données.
- Permettre une **intégration facile** de nouvelles sources.
- Optimiser les **performances** de chargement.
- Fournir une **traçabilité** claire des opérations.

## 📚 Documentation Approfondie

Pour une analyse complète de l'architecture, des composants, des flux de données et des décisions de conception, veuillez consulter notre [Documentation Détaillée du Système](https://www.notion.so/dinh/docs/SYSTEM_OVERVIEW.md).

## 📋 Fonctionnalités Clés

- **Architecture modulaire de chargement** : Système extensible avec interface `AbstractLoader`.
- **Support de sources multiples** :
  - Confluence (pages, blogs, pièces jointes)
  - SharePoint (documents, sites)
  - Google Cloud Storage (fichiers et documents)
  - Sources personnalisées via le loader "Basic"
- **Orchestration des chargements** : Planification automatisée, gestion d'étapes granulaires et reprise sur erreur via GCS.
- **REST API** : Interface complète pour l'intégration avec d'autres systèmes (kbot-back, kbot-embedding).
- **Sécurité** : Gestion centralisée des secrets (Google Secret Manager) et authentification aux API.
- **Optimisations (notamment pour Confluence)** : Pagination parallèle, retry, Circuit Breaker, suivi des changements.
- **Déploiement Cloud Native** : Conçu pour Cloud Run avec planification via Cloud Scheduler.

## 🏗️ Architecture Simplifiée

```mermaid
%%{init: {'layout': 'elk', 'elk.algorithm': 'force'}}%%
graph TD
    subgraph Cloud_Platform["☁️ Plateforme Cloud Google"]
        direction TB
        GCP_Cloud_Scheduler["⏰ 'Cloud Scheduler'"] -->|"'Déclenche'"| KLS["⚙️ 'kbot-load-scheduler (Cloud Run)'"]
        KLS -->|"'Secrets'"| GCP_Secret_Manager["🗄️ 'Secret Manager'"]
        KLS -->|"'Tâches/État'"| GCP_GCS["📦 'GCS (Working Bucket)'"]
    end

    subgraph Knowledge_Bot_Ecosystem["🤖 Écosystème Knowledge Bot"]
        direction TB
        KLS -->|"'Lit Config Sources'"| KBOT_Back_API["📡 'kbot-back API'"]
        KLS -->|"'Indexe/Supprime Docs'"| KBOT_Embedding_API["📡 'kbot-embedding API'"]
    end

    subgraph External_Data_Sources["🌐 Sources de Données Externes"]
        direction TB
        Confluence["📄 'Confluence'"]
        SharePoint["📄 'SharePoint'"]
        GCS_Source["📦 'GCS Source'"]
        Basic_API["📡 'API Basic'"]
    end

    KLS -->|"'Via Loaders'"| External_Data_Sources

    %% Styling
    style Cloud_Platform fill:#1E3A5F,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Knowledge_Bot_Ecosystem fill:#3D2B56,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style External_Data_Sources fill:#4A4A4A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style KLS fill:#2E4B6A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
```

[Mermaid Live](https://mermaid.live/view#pako:eNqtVctq20AU_ZVhQnAKsiPLtmxrUYgfDSEJNZVpoVURY-naFpZnzGhE7CZZFLIJFFIo9LUpLaU0uy6795_0B9pP6EhjO3bqQKCRQIzO3HvumTOvY-wxH7CFe5yM-qjdcCiSTxR3FFAPWey7rZCILuPDZw7-9f7l758XKEEggUCFoF3GeiE4-LkiSB4_4OCJgFHUrl2hu_WWq1htrw9-HAJPaC9-oIxiWsAZyYay2fsnDs40ppdeCFR2SfQE7R_YSdLHD4mWzKDDRDZkxM9G81y0pcgexfReZkWVTJ2T2uBxEFHKmMhS_-4hoaSXqvrz6d1ZWkH1oFnPjYTt6RcpINqengsiFrS7dTvlevMNZWQbbT1hfBDQHqrF3gDEkj6gvkOvzcA-ZUch-D1wa0y4TY9Fk0jAMGX8-hZNzxUy_S6nYhGLZOwt5mJJ-kEgUJ3RbtBDNou5B8qW_drDtlsj3sDdae2pUXyeGd6RKJLojW7sUR_GsG3HoxEPpLoG85ZIm8MO-L604V9mmHet0q9zpzkWwCkJ3QYRxJ0pT9levZ4PBPlJbUqnl7KtEiC6hTuJHWEsFx0oeWdyhS6g1VHbfcKhxQIqFqFX0GqoXAEznSuLQkGroTUSBd6KPbKt0LWuLHn_OCDoQO4I4MrytT7N8zY3kS0moTR85q78gWtbH3WDMLQ28s3CTumBFgnOBmBtFAqFWTt7FPiibxmjseaxkHFro6knr5bkZdmIeIGYWHquulzihrU9q1VoGLWSeVe11jowq1TcSd47G5WcBsVrNIs18794sYaHwIck8OUZfZxUcbDow1CetJZs-oQPHKwpPCQTFgvVAaHEHXoq80ksmD2hHrYEj0HD8ciXZ3cjIHIHDefgiFBsHeMxtirVnGlWC0bB1Et53dRNDU-wlc8bObNS0PPJR89X9eKphl8wJgn0XLmiG-VypWoaeqlaKeY1DH4gGD9UV0t6w6QlnqYJqiJnca-PrS4Jo4WoZpq2ALlc28DrLKYCW0ZZ1-QtlRiREJz-BdtbOps)

*Pour un diagramme d'architecture détaillé, consultez la [Documentation Détaillée du Système](https://www.notion.so/dinh/docs/SYSTEM_OVERVIEW.md).*

## 🚀 Installation

### Prérequis

- Python 3.9+
- Accès à Google Cloud Platform (GCP)
- Accès aux instances de service (Confluence, SharePoint, etc.) selon les besoins

### Installation standard

```bash
# Cloner le dépôt
git clone <https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler.git>
cd kbot-load-scheduler

# Installer les dépendances
pip install -r requirements.txt
```

### Installation pour le développement

```bash
# Installer les dépendances de développement
pip install -r requirements.txt -r tests/test-requirements.txt
```

## 🔧 Configuration

1. **Variables d'environnement** : Voir `.env.example` pour la structure. Créez un fichier `.env` pour votre configuration.
2. **Secrets d'accès aux sources** : Gérés via Google Secret Manager. Voir la documentation dans `src/kbotloadscheduler/secret/secret_manager.py` et le guide de configuration des secrets locaux pour les tests dans `conf/etc/secrets/tests/README_SECRETS.md`.

   Exemples de clés de secrets :

  - `{perimeter_code}-confluence-credentials`
  - `{perimeter_code}-sharepoint-credentials`

## 📊 Utilisation

### Démarrer le service

```bash
# Démarrage local pour développement (nécessite un .env configuré)
make start

# Ou avec uvicorn directement
uvicorn src.kbotloadscheduler.main:app --host 0.0.0.0 --port 8080
```

### Utilisation de l'API

La documentation OpenAPI (Swagger UI) est disponible sur `http://localhost:8080/docs` (ou l'URL de votre service déployé) lorsque le service est en cours d'exécution.

### Exemples d'appels (Gestion des sources)

```bash
# Créer une nouvelle source
curl -X POST "<http://localhost:8080/sources/>" \\
  -H "Content-Type: application/json" \\
  -d '{
    "code": "confluence_docs",
    "label": "Documentation Confluence",
    "src_type": "confluence",
    "configuration": "{\\"spaces\\": [\\"DOCS\\", \\"TECH\\"], \\"max_results\\": 1000}",
    "domain_code": "engineering",
    "perimeter_code": "main"
  }'

# Lister les sources
curl -X GET "<http://localhost:8080/sources/>"

```

*Consultez la [Documentation Détaillée du Système](https://www.notion.so/dinh/docs/SYSTEM_OVERVIEW.md) ou la Swagger UI pour la liste complète des endpoints et leurs détails.*

### Utilisation de Docker

```bash
# Construire l'image
docker build -t kbot-load-scheduler .

# Exécuter le conteneur (exemple minimal)
docker run -p 8080:8080 \\
  -e ENV=local \\
  -e PATH_TO_SECRET_CONFIG=/app/conf/etc/secrets/local \\
  # ... autres variables d'environnement nécessaires ...
  kbot-load-scheduler
```

## 🧪 Tests

### Exécuter les tests

```bash
# Tests complets (nécessite configuration d'environnement de test, voir Makefile)
make unit-tests

# Ou directement avec pytest
pytest

# Tests avec couverture
pytest --cov=src

# Tests spécifiques à un module (ex: Confluence)
pytest tests/loader/test_confluence_end_to_end.py -v

```

### Analyse de Sécurité (Bandit)

```bash
# Exécuter l'analyse de sécurité
make bandit
# ou via le script directement
./scripts/bandit_report.sh
```

### 🔧 Améliorations Récentes des Tests

✅ **Corrections apportées** :

- Tests end-to-end Confluence améliorés.
- Mocks robustes (`MockConfluenceAPI`, `MockGCSClient`).
- Meilleure isolation des tests et validation Pydantic dans les fixtures.

📚 **Documentation des tests** :

- Guide des secrets pour les tests : `conf/etc/secrets/tests/README_SECRETS.md`
- Tests Confluence : `src/kbotloadscheduler/loader/confluence/tests/README.md` et `src/kbotloadscheduler/loader/confluence/docs/TESTING_MOCKS.md`

## 📖 Documentation Complémentaire

- **Documentation API** : Disponible via Swagger UI sur `/docs` lorsque le service est lancé.
- [**Documentation Détaillée du Système**](https://www.notion.so/dinh/docs/SYSTEM_OVERVIEW.md) : Architecture, flux, composants.
- **Guides des Loaders** :
  - Confluence : `src/kbotloadscheduler/loader/confluence/README.md`
  - SharePoint : `src/kbotloadscheduler/loader/sharepoint/README.md`
  - GCS : `src/kbotloadscheduler/loader/gcs/README.md`
- **Gestion des Secrets pour Tests** : `conf/etc/secrets/tests/README_SECRETS.md`