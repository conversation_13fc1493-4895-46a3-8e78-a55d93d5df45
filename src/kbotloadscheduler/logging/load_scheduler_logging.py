LOAD_SCHEDULER_LOG_PREFIX = "[LoadScheduler]"

def log_action(perimeter_code: str, url: str, treatment_file: str):
    print(LOAD_SCHEDULER_LOG_PREFIX + " [%s] %s % s" % (perimeter_code, url, treatment_file))

def log_exception(perimeter_code: str, url: str, treatment_file: str, exception: Exception):
    print(LOAD_SCHEDULER_LOG_PREFIX + " [%s] Failed treatment %s %s => %s" % (perimeter_code, url, treatment_file,exception))

def log_remove_document_error(perimeter_code: str, remove_document_url: str, status_code: int):
    print(LOAD_SCHEDULER_LOG_PREFIX + " [%s] Embedding remove request failed %s code=%s" % (perimeter_code,
                                                                                        remove_document_url,
                                                                                        status_code))

def log_treatments_launched(perimeter_code: str, treatment_name: str, nb_treatments:int):
    print(LOAD_SCHEDULER_LOG_PREFIX + " [%s] %s %s treatments" %(perimeter_code, treatment_name, nb_treatments))

def log_message(perimeter_code: str, message: str):
    print(LOAD_SCHEDULER_LOG_PREFIX + " [%s] %s" % (perimeter_code, message))