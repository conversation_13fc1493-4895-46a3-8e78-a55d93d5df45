import google.auth.transport.requests
import google.oauth2.id_token


def build_auth_header(env, audience):
    """
    Build header for requests.
    If env is local, no authentication. Else add X-Serverless-Authorization header with SA token
    """
    headers = {}
    if env != 'local' and env != 'tests':
        # Cloud Run uses your service's hostname as the `audience` value
        # audience = 'https://my-cloud-run-service.run.app/'
        auth_req = google.auth.transport.requests.Request()
        id_token = google.oauth2.id_token.fetch_id_token(auth_req, audience)
        headers['X-Serverless-Authorization'] = f"Bearer {id_token}"

    return headers
