from typing import List
import requests

from ..bean.beans import SourceBean
from .auth_header import build_auth_header
from .orange_jwt import OrangeJwt
from ..secret.secret_manager import ConfigWithSecret


class KbotBackApi:
    """Classe effectuant les appels vers l'api kbot-back pour récupérer les périmètres, les domaines et les sources

    :field url: l'url d'accès à l'api kbot-back
    """
    def __init__(self, url: str, env: str = 'local', config_with_secret: ConfigWithSecret = None) -> None:
        """Constructeur

        :param url: url de l'api knowledge bot
        :param env: l'environnement
        :param config_with_secret: configuration
        """
        self.url = url
        self.audience = url
        self.env = env
        self.config_with_secret = config_with_secret
        self.orange_jwt = None
        if self.env != 'local' and self.env != 'tests' and config_with_secret is not None:
            config = config_with_secret.get_config()
            okapi_url = config.get('okapi_url')
            client_id = config_with_secret.get_kbot_loadscheduler_client_id()
            client_secret = config_with_secret.get_kbot_loadscheduler_client_secret()
            scope = config.get('kbot_loadscheduler_client_scope')
            self.audience = config.get('kbot_back_api_cloud_run_url')
            self.orange_jwt = OrangeJwt(token_url=okapi_url,
                                        client_id=client_id, client_secret=client_secret,
                                        scopes=scope.split(' '))

    # Construction du header avec token okapi si les infos sont fournies
    def build_okapi_auth_header(self):
        headers = build_auth_header(self.env, self.audience)
        if self.orange_jwt is not None:
            okapi_token = self.orange_jwt.get_token()
            headers['Authorization'] = f"bearer {okapi_token}"
        return headers

    def get_perimeters(self) -> List[dict]:
        """Récupération de la liste de tous les périmètres

        :return: la liste des périmètres
        """
        get_perimeters_url = f"{self.url}/perimeter/list"
        headers = self.build_okapi_auth_header()
        perimeters_response = requests.get(get_perimeters_url, timeout=300,
                                           headers=headers)
        return perimeters_response.json()

    def get_sources(self, perimeter_code: str) -> List[SourceBean]:
        """Liste les sources à récupérer pour un périmètre

        :param perimeter_code: code du périmètre pour lequel lister les sources
        :return: la liste des souces à récupérer pour ce périmètre
        """
        perimeter = self.get_perimeter_by_code(perimeter_code)
        domains = self.get_perimeter_domains(perimeter.get('id'))
        sources = []
        for domain in domains:
            domain_sources = self.get_domain_sources_to_load(domain.get('id'))
            sources.extend([self.as_source_bean(perimeter_code, domain.get('code'), source)
                            for source in domain_sources])
        return sources

    def get_perimeter_by_code(self, perimeter_code: str) -> dict:
        """Récupération d'un périmètre à partir de son code

        :param perimeter_code: code du périmètre à récupérer
        :return: le périmètre récupéré
        """
        get_perimeter_url = f"{self.url}/perimeter/bycode/{perimeter_code}"
        headers = self.build_okapi_auth_header()
        perimeter_response = self.request_get(get_perimeter_url, headers)
        return perimeter_response.json()

    def get_perimeter_domains(self, perimeter_id: int) -> List[dict]:
        """Récupération de tous les domaines d'un périmètre

        :param perimeter_id: id du périmètre pour lequel on veut lister les domaines
        :return: les domaines du périmètre
        """
        get_domains_url = f"{self.url}/perimeter/{perimeter_id}/domains"
        headers = self.build_okapi_auth_header()
        domains_response = self.request_get(get_domains_url, headers)
        return domains_response.json()

    def get_domain_sources_to_load(self, domain_id: int) -> List[dict]:
        """Récupération des sources d'un domaine

        :param domain_id: id du domaine pour lequel récupérer les sources
        :return: les sources du domaine
        """
        get_sources_url = f"{self.url}/domain/{domain_id}/sourcestoload"
        headers = self.build_okapi_auth_header()
        sources_response = self.request_get(get_sources_url, headers)
        return sources_response.json()

    def set_source_done(self, source_id: int):
        """Positionne la source comme ayant été prise en compte pour le chargement.

        :param source_id: l'identifiant de la source à marquer comme prise en compte
        """
        set_source_done_url = f"{self.url}/source/{source_id}/done"
        headers = self.build_okapi_auth_header()
        self.request_put(set_source_done_url, headers)

    def request_get(self, url, headers):
        if self.orange_jwt is not None:
            return requests.get(url, timeout=300, headers=headers, verify=self.orange_jwt.CA_CERTIFICATE)
        return requests.get(url, timeout=300, headers=headers)

    def request_put(self, url, headers):
        if self.orange_jwt is not None:
            return requests.put(url, timeout=300, headers=headers, verify=self.orange_jwt.CA_CERTIFICATE)
        return requests.put(url, timeout=300, headers=headers)

    @staticmethod
    def as_source_bean(perimeter_code, domain_code, source) -> SourceBean:
        """Transforme le dictionnaire source récupéré de l'api en un objet SourceBean

        :param perimeter_code: code du périmètre
        :param domain_code: code du domaine
        :param source: source à transformer
        :return: le SourceBean correspondant à la source avec les codes de périmètre et de domaine
        """
        return SourceBean(
            id=source.get('id'),
            code=source.get('code'),
            label=source.get('label'),
            src_type=source.get('src_type'),
            configuration=source.get('configuration'),
            last_load_time=source.get('last_load_time'),
            load_interval=source.get('load_interval'),
            domain_code=domain_code,
            perimeter_code=perimeter_code,
            force_embedding=source.get('force_embedding', False),
        )
