from fastapi import APIRouter, Depends, Response, status, Path, Body
from dependency_injector.wiring import inject, Provide
from typing import List, Annotated

from ..dependency.container import Container
from ..loader.abstract_loader import LoaderException
from ..logging.load_scheduler_logging import log_exception

from ..service.loader_service import LoaderService
from ..bean.beans import DocumentAnswerBean, DocumentWithMetadataBean
from ..bean.api_post_beans import LoaderGetListFileBean, GetDocumentFileBean


"""Routes de lancement des loader

Ces routes vont déclencher les actions suivantes pour les loader :
  * /loader/list : lance la récupération de la liste des documents
  * /loader/documents : lance la récupération des documents et leur copie sur GCS
"""

TAG = "loader"
DESCRIPTION = """Appels du loader associé à la source à charger
                pour lister les documents de la source et les récupérer sur GCP"""
router = APIRouter()


@router.post("/loader/list/{perimeter_code}")
@inject
async def get_document_list(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    get_list_file_bean: Annotated[LoaderGetListFileBean, Body(
        description="""Fichier getlist contenant la définition de la source
            pour laquelle on veut récupérer la liste des documents""")],
    loader_service: LoaderService = Depends(Provide[Container.loader_service])
) -> List[DocumentAnswerBean]:
    """Lancement de la récupération de la liste des documents présents dans la source
    \f
    :param perimeter_code: le code du périmètre. Cela permet de savoir sur quel bucket écrire la liste
    :param get_list_file_bean: le fichier contenant la configuration de la source
    pour laquelle on veut lister les documents.
    Ce fichier a été généré par l'appel de l'api sources/load ou sources/loadall.
    :param loader_service: le service qui ordonnance l'appel au Loader en fonction de la source
    :return: la liste des documents récupérées
    """
    get_list_file = get_list_file_bean.get_list_file
    return loader_service.get_document_list(perimeter_code, get_list_file)


@router.post("/loader/document/{perimeter_code}")
@inject
async def get_document(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    document_get_file_bean: Annotated[GetDocumentFileBean, Body(
        description="""Fichier getdoc contenant la définition de la source
            et le document à récupérer pour cette source""")],
    loader_service: LoaderService = Depends(Provide[Container.loader_service])
) -> DocumentWithMetadataBean:
    """Lancement de la récupération des documents d'une source
    \f
    :param perimeter_code: le code du périmètre. Cela permet de savoir sur quel bucket récupérer le document
    :param document_get_file: fichier contenant la configuration de la source
    et du document à récupérer.
    Ce fichier a été généré par l'appel de l'api embedding/comparelist
    :param loader_service: le service qui ordonnance l'appel au Loader en fonction de la source du documents
    :return: les metadatas du document à récupérer
    """
    try:
        return loader_service.get_document(perimeter_code, document_get_file_bean.document_get_file)
    except LoaderException as e:
        log_exception(perimeter_code, "/loader/document/" + perimeter_code, document_get_file_bean.document_get_file, e)
        return Response(status_code=status.HTTP_400_BAD_REQUEST)

