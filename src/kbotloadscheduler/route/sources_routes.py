from fastapi import APIRouter, Depends, Path
from dependency_injector.wiring import inject, Provide
from typing import List, Annotated

from ..dependency.container import Container

from ..service.sources_service import SourcesService
from ..bean.beans import SourceBean


"""Routes sources pour récupérer les sources à charger
"""

TAG = "sources"
DESCRIPTION = "Récupération des sources à charger"
router = APIRouter()


@router.post("/sources/loadall")
@inject
async def load_all_sources(
    sources_service: SourcesService = Depends(Provide[Container.sources_service])
) -> List[SourceBean]:
    """Lancement de la récupération des documents pour toutes les sources de tous les périmètres
    \f
    :param sources_service: le service qui ordonnance les différents appels et traitements
    :type sources_service: SourcesService
    :return: la liste des sources que l'on va récupérer sur cette session
    :rtype: List[SourcesBean]
    """
    return sources_service.load_all_sources()


@router.post("/sources/load/{perimeter_code}")
@inject
async def load_sources(
    perimeter_code: Annotated[str, Path(description="Code du périmètre pour lequel on veut remonter les sources")],
    sources_service: SourcesService = Depends(Provide[Container.sources_service])
) -> List[SourceBean]:
    """Lancement de la récupération des documents pour toutes le sources du périmètre
    \f
    :param perimeter_code: code du périmètre pour lequel on veut charger les sources
    :type perimeter_code: str
    :param sources_service: le service qui ordonnance les différents appels et traitements
    :type sources_service: SourcesService
    :return: la liste des sources que l'on va récupérer sur cette session
    :rtype: List[SourcesBean]
    """
    return sources_service.load_sources(perimeter_code)
