from fastapi import APIRouter, Depends, Path
from dependency_injector.wiring import inject, Provide
from typing import List, Annotated

from ..dependency.container import Container

from ..service.schedule_service import ScheduleService
from ..bean.beans import TreatmentBean, LOAD_DATE_FORMAT, ResultBean
from datetime import datetime

"""Routes schedule pour récupérer la liste des fichiers à traiter avec les listes des urls à appeler
"""

TAG = "schedule"
DESCRIPTION = "Récupération des url à appeler pour lancer les traitements à réaliser"
router = APIRouter()


@router.get("/schedule/treatments/{perimeter_code}/{date}")
@inject
async def list_treatments(
    perimeter_code: Annotated[str, Path(description="Perimetre (ebotman/mktsearch ....)")],
    date: Annotated[str, Path(description="Date courante au format YYYYMMDDHHMMSS")],
    schedule_service: ScheduleService = Depends(Provide[Container.schedule_service])
) -> List[TreatmentBean]:
    """Lancement de la récupération des traitements à réaliser
    \f
    :param date: date au format YYYYMMDDHHMMSS
    : type date: str
    :param schedule_service: le service qui ordonnance les différents appels et traitements
    :type schedule_service: ScheduleService
    :return: la liste des traitements à réaliser
    :rtype: List[TreatmentBean]
    """
    return schedule_service.list_treatments_for_perimeter(perimeter_code, datetime.strptime(date, LOAD_DATE_FORMAT))


@router.post("/schedule/treatments/{perimeter_code}/launch")
@inject
async def launch_treatments(
    perimeter_code: Annotated[str, Path(description="Perimetre (ebotman/mktsearch ....)")],
    schedule_service: ScheduleService = Depends(Provide[Container.schedule_service]),
) -> ResultBean:
    """
    Launch treatments needed
    To call from a cloud scheduler
    Args:
        schedule_service:
        perimeter_code
    """
    return schedule_service.launch_treatments_for_perimeter(perimeter_code)
