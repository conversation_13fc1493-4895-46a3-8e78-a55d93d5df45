from fastapi import APIRouter, Depends, Response, status, Path, Body
from dependency_injector.wiring import inject, Provide
from typing import List, Annotated

from ..apicall.kbot_embedding_api import KbotEmbeddingApi
from ..dependency.container import Container
from ..logging.load_scheduler_logging import log_exception

from ..service.document_service import DocumentService
from ..bean.beans import DocumentAnswerBean
from ..bean.api_post_beans import RepoDocumentListFileBean, EmbeddDocumentFileBean, RemoveDocumentFileBean


"""Lancement des actions concernant l'embedding des documents

On dispose des routes suivantes :
  * document/compare : permet de comparer la liste des documents coté repo de la source
    et les documents déjà embeddés pour générer la liste des documents à embedder et des documents à supprimer
  * document/embedd : permet d'embedder un document
  * document/remove : permet de supprimer un document embeddé

"""

TAG = "document"
DESCRIPTION = "Gestion de l'embedding des documents"
router = APIRouter()


@router.post("/document/compare/{perimeter_code}")
@inject
async def compare_list(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    repo_document_list_file_bean: Annotated[RepoDocumentListFileBean, Body(
        description="""Fichier list contenant la liste des documents présents sur la source
            """)],
    document_service: DocumentService = Depends(Provide[Container.document_service])
) -> List[DocumentAnswerBean]:
    """Comparaison de la liste des documents sur le repo et de la liste des documents embeddés.

    La date de dernière modification est prise en compte et si la date de l'embedding est plus ancienne
    alors le document est marqué à récupérer
    \f
    :param perimeter_code: code du périmètre. Permet de savoir où écrire la liste des documents à embedder
    et la liste des documents à supprimer
    :type perimeter_code: str
    :param repo_document_list_file_bean: fichier contenant la configuration de la source et
    la liste des documents sur le repo
    :param document_service: service ordonnançant l'appel à l'api embedding
    :return:
    """
    repo_document_list_file = repo_document_list_file_bean.repo_document_list_file
    return document_service.compare_document_list(perimeter_code, repo_document_list_file)


@router.post("/document/embedd/{perimeter_code}")
@inject
async def embedd_document(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    embedd_document_file_bean: Annotated[EmbeddDocumentFileBean, Body(
        description="""Fichier doc .metdata.json contenant les metadata du document à embedder""")],
    document_service: DocumentService = Depends(Provide[Container.document_service])
) -> DocumentAnswerBean:
    """Lancement de l'embedding d'un document récupéré
    \f
    :param perimeter_code: code du périmètre. Permet de savoir où récupérer les documents
    :type perimeter_code: str
    :param embedd_document_file_bean: fichier contenant la configuration de la source
    et la liste des documents à embedder
    :param document_service: service ordonnançant l'appel à l'api embedding
    :return:
    """
    embedd_document_metadata_file = embedd_document_file_bean.embedd_document_metadata_file
    try:
        result = document_service.embedd_document(perimeter_code, embedd_document_metadata_file)
        if result['status'] == 'ok':
            return result['document']
    except KbotEmbeddingApi.EmbeddingException as e:
        log_exception(perimeter_code, "/document/embedd/" + perimeter_code, embedd_document_metadata_file, e)
    return Response(status_code=status.HTTP_400_BAD_REQUEST)


@router.post("/document/remove/{perimeter_code}")
@inject
async def remove_documents(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    remove_document_file_bean: Annotated[RemoveDocumentFileBean, Body(
        description="""Fichier removedoc contenan la définition de la source et
            la liste des documents à supprimer""")],
    document_service: DocumentService = Depends(Provide[Container.document_service])
) -> List[DocumentAnswerBean]:
    """Lancement de la suppression de documents
    \f
    :param perimeter_code: code du périmètre. Permet de savoir sur quelle base supprimer les documents
    :type perimeter_code: str
    :param remove_document_file_bean: fichier contenant la configuration de la source et
    la liste des documents à supprimer
    :param document_service: service ordonnançant l'appel à l'api embedding
    :return:
    """
    remove_document_file = remove_document_file_bean.remove_document_file
    return document_service.remove_documents(perimeter_code, remove_document_file)
