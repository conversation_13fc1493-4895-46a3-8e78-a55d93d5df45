from ..abstract_loader import AbstractLoader, LoaderException
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean, Metadata
from kbotloadscheduler.gcs import gcs_utils
from .sharepoint_credentials import SharepointCredentials
from .sharepoint_client import Sharepoint<PERSON>lient, SharepointException
from kbotloadscheduler.gcs.gcs_utils import tidy_file_name
from datetime import datetime, timezone
import os


class SharepointLoader(AbstractLoader):
    """Loader pour sharepoint"""

    def __init__(self, config: ConfigWithSecret):
        super().__init__("sharepoint")
        self.sharepoint_url = os.getenv("URL_SERVICE_SHAREPOINT")
        self.config = config

    def get_document_list(self, source: SourceBean):
        sharepoint_client = self.get_sharepoint_client(source)
        site_name = source.get_expected_conf('site_name')
        relative_directory = source.get_expected_conf('relative_directory')
        site_relative_directory = f'/sites/{site_name}/{relative_directory}'
        source_folder_info = sharepoint_client.get_folder_info(site_relative_directory)
        source_files = self.get_files_and_subfolders(sharepoint_client, source_folder_info)
        return [self.as_document_bean(source.domain_code, source.code, file_info)
                for file_info in source_files]

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        site_name = source.get_expected_conf('site_name')
        relative_directory = source.get_expected_conf('relative_directory')
        site_relative_directory = f'/sites/{site_name}/{relative_directory}/'
        sharepoint_client = self.get_sharepoint_client(source)
        metadata = {}
        file_relative_path = document.name
        tidy_relative_path = tidy_file_name(file_relative_path.replace(site_relative_directory, ''))
        # file_relative_path starts with /sites (it's the ServerRelativeUrl given by sharepoint)
        source_url = self.sharepoint_url + file_relative_path
        destination_path = f'{output_path}/{tidy_relative_path}'
        metadata[Metadata.DOCUMENT_ID] = document.id
        metadata[Metadata.DOCUMENT_NAME] = document.name
        metadata[Metadata.SOURCE_URL] = source_url
        metadata[Metadata.LOCATION] = destination_path
        try:
            file_info = sharepoint_client.get_file_info(file_relative_path)
            file_content = sharepoint_client.get_file_bytes_content(file_relative_path)
            gcs_utils.create_file_with_bytes_content(destination_path, file_content)
            if gcs_utils.exists_file_gcs(destination_path):
                file_creation_time = self.parse_datetime(file_info.get('TimeCreated', ''))
                file_modification_time = self.parse_datetime(file_info.get('TimeLastModified', ''))
                metadata = {
                    **metadata,
                    Metadata.CREATION_TIME: file_creation_time,
                    Metadata.MODIFICATION_TIME: file_modification_time
                }
                return metadata
            else:
                raise LoaderException(f'File {destination_path} not created at destination')
        except SharepointException as ex:
            raise LoaderException(f'{str(ex)} ({ex.status_code})')


    def get_sharepoint_client(self, source: SourceBean):
        client_config = self.config.get_sharepoint_client_config(source.perimeter_code)
        client_private_key = self.config.get_sharepoint_client_private_key(source.perimeter_code)
        sharepoint_credentials = SharepointCredentials(
            tenant_id=client_config.get('tenant_id'),
            client_id=client_config.get('client_id'),
            client_certificate=client_config.get('certificate_thumbprint'),
            client_private_key=client_private_key,
            client_key_password=client_config.get('password')
        )
        site_name = source.get_expected_conf('site_name')
        client = SharepointClient(sharepoint_credentials, self.sharepoint_url, site_name)
        return client

    def get_files_and_subfolders(self, sharepoint_client, folder_info):
        folder_relative_url = folder_info.get('ServerRelativeUrl')
        folder_files = sharepoint_client.get_folders_files(folder_relative_url)
        folder_sub_folders = sharepoint_client.get_sub_folders(folder_relative_url)
        for sub_folder in folder_sub_folders:
            sub_folder_files = self.get_files_and_subfolders(sharepoint_client, sub_folder)
            folder_files.extend(sub_folder_files)
        return folder_files

    def as_document_bean(self, domain_code, source_code, file_info):
        file_path = file_info.get('ServerRelativeUrl', '')
        file_modification_time = self.parse_datetime(file_info.get('TimeLastModified', ''))
        return DocumentBean(
            id='|'.join([domain_code, source_code, file_path]),
            name=file_path,
            path=self.sharepoint_url+file_path,
            modification_time=file_modification_time
        )

    @classmethod
    def parse_datetime(cls, string_datetime: str) -> datetime:
        """
        Traduit la date et l'heure reçues de l'api en un objet datetime.
        Le format reçu est 2024-09-03T13:00:44Z compatible avec la norme ISO 8601
        """
        d = datetime.fromisoformat(
            string_datetime[:-1]
        ).astimezone(timezone.utc)
        return d
