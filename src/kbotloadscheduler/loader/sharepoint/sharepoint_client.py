import requests
from .sharepoint_credentials import SharepointCredentials


class SharepointClient:
    def __init__(self, sharepoint_credentials: SharepointCredentials, sharepoint_url, site_name: str):
        self.access_token = sharepoint_credentials.get_access_token()
        self.site_name = site_name
        self.site_url = sharepoint_url + '/sites/' + site_name
        self.headers = {'Content-Type': 'application/x-www-form-urlencoded'}

    def get_base_folders(self):
        full_url = self.build_full_url('folders', 'Name,ServerRelativeUrl,TimeCreated,TimeLastModified,UniqueId')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_folder_info(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFolderByServerRelativeUrl('{protected_relative_url}')",
                                       'Name,ServerRelativeUrl,TimeCreated,TimeLastModified,UniqueId')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_sub_folders(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFolderByServerRelativeUrl('{protected_relative_url}')/folders",
                                       'Name,ServerRelativeUrl,TimeCreated,TimeLastModified,UniqueId')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_folders_files(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFolderByServerRelativeUrl('{protected_relative_url}')/files",
                                       'Name,ServerRelativeUrl,TimeCreated,TimeLastModified,UniqueId')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_file_info(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFileByServerRelativeUrl('{protected_relative_url}')",
                                       'Name,ServerRelativeUrl,TimeCreated,TimeLastModified,UniqueId')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_properties(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFileByServerRelativeUrl('{protected_relative_url}')/properties",
                                       '')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_bytes_content(self, relative_url):
        protected_relative_url = relative_url.replace("'", "''")
        full_url = self.build_full_url(f"GetFileByServerRelativeUrl('{protected_relative_url}')/$value")
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return response.content

    def build_full_url(self, command: str, fields: str = ""):
        full_url = f"{self.site_url}/_api/web/{command}"
        if fields != "":
            full_url = full_url + f"?$select={fields}"
        return full_url

    def call_get(self, full_url):
        return requests.get(full_url, headers={
            'Accept': 'application/json;odata=verbose',
            'Authorization': f'Bearer {self.access_token}'})

    def prepare_list_response(self, response):
        json_response = response.json()
        return_response = json_response.get('d', {}).get('results', [])
        return [self.prepare_item(item) for item in return_response]

    def prepare_info_response(self, response):
        json_response = response.json()
        item = json_response.get('d', {})
        return self.prepare_item(item)

    def prepare_item(self, item):
        metadata = item.pop('__metadata', {})
        item['type'] = metadata.get('type', '')
        item['uri'] = metadata.get('uri', '')
        return item


class SharepointException(Exception):
    NOT_CRITICAL_CODES = [404]
    status_code: int

    def __init__(self, url, response):
        error_message = "Sharepoint call on url %s failed: " % url
        if response.status_code == 200:
            error_message += response.json().get('error_description')
        elif response.status_code in SharepointException.NOT_CRITICAL_CODES:
            error_message += "status_code is %s" % response.status_code
        else:
            error_message += "[critical] status_code is %s" % response.status_code
        super().__init__(error_message)
        self.status_code = response.status_code
