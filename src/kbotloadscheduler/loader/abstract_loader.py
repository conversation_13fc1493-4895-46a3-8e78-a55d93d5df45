from typing import List
from ..bean.beans import SourceBean, DocumentBean


class AbstractLoader:
    """Loader abstrait dont doivent hériter tous les loader"""

    def __init__(self, loader_type: str):
        self._loader_type = loader_type

    def check_type(self, source_type: str):
        return self._loader_type == source_type

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        pass

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str):
        pass


class LoaderException(Exception):
    pass
