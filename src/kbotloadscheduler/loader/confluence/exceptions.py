#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exceptions personnalisées pour le système RAG Confluence.
"""


class ConfluenceRAGException(Exception):
    """Exception de base pour toutes les exceptions du système RAG Confluence."""
    pass


class ConfigurationError(ConfluenceRAGException):
    """Erreur de configuration."""
    pass


class AuthenticationError(ConfluenceRAGException):
    """Erreur d'authentification à l'API Confluence."""
    pass


class APIError(ConfluenceRAGException):
    """Erreur lors de l'appel à l'API Confluence."""
    def __init__(self, message, status_code=None, response=None):
        self.status_code = status_code
        self.response = response
        super().__init__(message)


class ContentNotFoundError(ConfluenceRAGException):
    """Contenu non trouvé dans Confluence."""
    pass


class AttachmentProcessingError(ConfluenceRAGException):
    """Erreur lors du traitement d'une pièce jointe."""
    def __init__(self, message, attachment_id=None, file_name=None):
        self.attachment_id = attachment_id
        self.file_name = file_name
        super().__init__(message)


class ContentProcessingError(ConfluenceRAGException):
    """Erreur lors du traitement d'un contenu."""
    def __init__(self, message, content_id=None, content_title=None):
        self.content_id = content_id
        self.content_title = content_title
        super().__init__(message)


class SecurityValidationError(ConfluenceRAGException):
    """Erreur de validation de sécurité."""
    pass


class RateLimitExceededError(ConfluenceRAGException):
    """Limite de taux d'appels API dépassée."""
    def __init__(self, message, retry_after=None):
        self.retry_after = retry_after
        super().__init__(message)


class CircuitOpenError(ConfluenceRAGException):
    """Exception levée lorsque le Circuit Breaker est ouvert."""
    def __init__(self, message, service_name=None, reset_timeout=None, time_remaining=None):
        self.service_name = service_name
        self.reset_timeout = reset_timeout
        self.time_remaining = time_remaining
        super().__init__(message)