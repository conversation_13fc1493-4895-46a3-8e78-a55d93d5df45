#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Récupération et orchestration de contenu synchrone pour le système RAG de Confluence.
"""

import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional

from .sync_attachment_processor import SyncAttachmentProcessor
from .content_chunker import ContentChunker
from ..models import ContentItem
from ..sync_client import SyncConfluenceClient
from ..config import SearchCriteria, ProcessingConfig
from ..utils import TextProcessor
from ..thread_pool_manager import get_thread_pool_manager
from ..exceptions import ContentProcessingError


class SyncContentRetriever:
    """Récupérateur de contenu Confluence synchrone avec gestion des threads."""

    def __init__(
            self,
            client: SyncConfluenceClient,
            processing_config: Optional[ProcessingConfig] = None,
            storage_config=None
    ):
        """
        Initialise le récupérateur avec le client et la configuration.

        Args:
            client: Client Confluence synchrone
            processing_config: Configuration de traitement (optionnelle)
            storage_config: Configuration de stockage (optionnelle)
        """

        self.client = client
        self.logger = logging.getLogger(__name__)

        # Use provided configuration or create default
        self.config = processing_config or ProcessingConfig.from_env()

        # Initialize components
        self.attachment_processor = SyncAttachmentProcessor(client, self.config, storage_config)
        self.chunker = ContentChunker(self.config.chunk_size, self.config.overlap_size)

        # Thread pool manager for parallel processing
        self.thread_pool_manager = get_thread_pool_manager(self.config.thread_pool_config)

        # Track retrieval statistics
        self._stats = {
            "content_retrieved": 0,
            "attachments_processed": 0,
            "errors": 0
        }

        self.logger.info(
            f"SyncContentRetriever initialized with chunk_size={self.config.chunk_size}, "
            f"overlap_size={self.config.overlap_size}"
        )

    def retrieve_content(self, content_id: str, process_attachments: bool = True) -> ContentItem:
        """
        Récupère le contenu Confluence avec ses pièces jointes de manière synchrone.

        Args:
            content_id: ID du contenu à récupérer
            process_attachments: Indique s'il faut traiter les pièces jointes

        Returns:
            ContentItem avec le contenu et les pièces jointes traités
        """

        try:
            self.logger.info(f"Retrieving content {content_id}")

            # Retrieve content
            content_item = self.client.get_content(content_id)

            # Extract plain text from HTML body
            if content_item.body_view:
                content_item.body_plain = TextProcessor.html_to_plain_text(content_item.body_view)
                self.logger.debug(f"Extracted {len(content_item.body_plain)} characters of plain text")

            # Process attachments if requested
            if process_attachments:
                self._process_content_attachments(content_item)

            self._stats["content_retrieved"] += 1
            self.logger.info(f"Content {content_id} retrieved successfully")

            return content_item

        except Exception as e:
            self.logger.error(f"Failed to retrieve content {content_id}: {e}")
            self._stats["errors"] += 1
            raise ContentProcessingError(
                f"Content retrieval failed for {content_id}: {str(e)}",
                content_id=content_id
            )

    def _process_content_attachments(self, content_item: ContentItem) -> None:
        """Traite les pièces jointes d'un élément de contenu de manière synchrone."""
        try:
            attachments = self.client.get_attachments(content_item.id)
            if not attachments:
                self.logger.debug(f"No attachments found for content {content_item.id}")
                return

            self.logger.info(f"Processing {len(attachments)} attachments for content {content_item.id}")

            # Process attachments in parallel using thread pool
            processed_attachments = []
            
            # Submit all attachment processing tasks to thread pool
            with ThreadPoolExecutor(max_workers=self.config.max_parallel_downloads) as executor:
                future_to_attachment = {
                    executor.submit(self.attachment_processor.process_attachment, attachment): attachment
                    for attachment in attachments
                }

                # Collect results as they complete
                for future in as_completed(future_to_attachment):
                    attachment = future_to_attachment[future]
                    try:
                        processed_attachment = future.result()
                        processed_attachments.append(processed_attachment)
                        self._stats["attachments_processed"] += 1
                    except Exception as e:
                        self.logger.error(f"Failed to process attachment {attachment.id}: {e}")
                        self._stats["errors"] += 1
                        # Add the attachment with error status
                        attachment.processing_status = "failed"
                        attachment.processing_error = str(e)
                        processed_attachments.append(attachment)

            content_item.attachments = processed_attachments
            self.logger.info(f"Processed {len(processed_attachments)} attachments for content {content_item.id}")

        except Exception as e:
            self.logger.error(f"Failed to process attachments for content {content_item.id}: {e}")
            self._stats["errors"] += 1

    def get_retrieval_stats(self) -> Dict[str, int]:
        """Get retrieval statistics."""
        attachment_stats = self.attachment_processor.get_processing_stats()
        return {
            **self._stats,
            **{f"attachment_{k}": v for k, v in attachment_stats.items()}
        }

    def reset_stats(self) -> None:
        """Reset all statistics."""
        self._stats = {
            "content_retrieved": 0,
            "attachments_processed": 0,
            "errors": 0
        }
        self.attachment_processor.reset_stats()

    def search_and_retrieve(
            self,
            criteria: SearchCriteria,
            process_attachments: bool = True
    ) -> List[ContentItem]:
        """
        Recherche et récupère du contenu selon les critères spécifiés de manière synchrone.

        Args:
            criteria: Critères de recherche
            process_attachments: Indique s'il faut traiter les pièces jointes

        Returns:
            Liste d'objets ContentItem avec le contenu traité
        """
        try:
            self.logger.info(f"Starting search and retrieval with criteria: {criteria}")

            # Search for content
            content_items = self.client.search_content(criteria)
            self.logger.info(f"Found {len(content_items)} content items")

            if not content_items:
                return []

            # Process each content item in parallel using thread pool
            detailed_items = []
            
            with ThreadPoolExecutor(max_workers=self.config.max_thread_workers) as executor:
                # Submit content processing tasks
                future_to_item = {
                    executor.submit(self._process_single_content, item, process_attachments, criteria): item
                    for item in content_items
                }

                # Collect results as they complete
                for i, future in enumerate(as_completed(future_to_item), 1):
                    item = future_to_item[future]
                    try:
                        self.logger.info(f"Processing content {i}/{len(content_items)}: {item.id} - {item.title}")
                        detailed_item = future.result()
                        if detailed_item:
                            detailed_items.append(detailed_item)
                    except Exception as e:
                        self.logger.error(f"Failed to process content {item.id}: {e}")
                        self._stats["errors"] += 1
                        continue

            self.logger.info(f"Successfully processed {len(detailed_items)} content items")
            return detailed_items

        except Exception as e:
            self.logger.error(f"Search and retrieval failed: {e}")
            self._stats["errors"] += 1
            raise ContentProcessingError(f"Search and retrieval failed: {str(e)}")

    def _process_single_content(
            self, 
            item: ContentItem, 
            process_attachments: bool, 
            criteria: SearchCriteria
    ) -> Optional[ContentItem]:
        """Process a single content item."""
        try:
            detailed_item = self.retrieve_content(item.id, process_attachments)

            # Process children recursively if requested
            if criteria.include_children and detailed_item.children:
                child_items = self._retrieve_children_recursively(
                    detailed_item,
                    criteria,
                    process_attachments,
                    current_depth=1
                )
                # Note: For simplicity, we're not adding children to the main list
                # This can be enhanced based on requirements

            return detailed_item

        except Exception as e:
            self.logger.error(f"Failed to process single content {item.id}: {e}")
            return None

    def _retrieve_children_recursively(
            self,
            parent: ContentItem,
            criteria: SearchCriteria,
            process_attachments: bool,
            current_depth: int = 1
    ) -> List[ContentItem]:
        """
        Récupère récursivement les pages enfants d'un contenu parent.
        Note: Simplified synchronous version - can be enhanced with parallel processing.
        """
        if not parent.children or current_depth >= getattr(criteria, 'max_children_depth', 3):
            return []

        child_items = []
        child_count = len(parent.children)

        self.logger.info(f"Processing {child_count} children for {parent.id} at depth {current_depth}")

        for i, child_info in enumerate(parent.children, 1):
            try:
                child_id = child_info.get('id')
                if not child_id:
                    self.logger.warning(f"Missing ID for child of {parent.id}")
                    continue

                child_title = child_info.get('title', 'Untitled')
                self.logger.debug(f"Processing child {i}/{child_count}: {child_id} - {child_title}")

                # Retrieve child content
                child_item = self.retrieve_content(child_id, process_attachments)
                child_items.append(child_item)

                # Recursively process grandchildren
                if child_item.children and current_depth < getattr(criteria, 'max_children_depth', 3):
                    grandchildren = self._retrieve_children_recursively(
                        child_item,
                        criteria,
                        process_attachments,
                        current_depth + 1
                    )
                    child_items.extend(grandchildren)

            except Exception as e:
                self.logger.error(f"Failed to process child {child_info.get('id', 'unknown')}: {e}")
                self._stats["errors"] += 1
                continue

        self.logger.info(f"Retrieved {len(child_items)} children for {parent.id}")
        return child_items
