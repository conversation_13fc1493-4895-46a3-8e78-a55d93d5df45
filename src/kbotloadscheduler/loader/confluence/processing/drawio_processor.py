#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Specialized processor for draw.io diagrams.
"""

import logging
import xml.etree.ElementTree as ET
import base64
import urllib.parse
import zlib
from typing import Optional

from bs4 import BeautifulSoup

from .enums import ExtractionResult, DrawIOMetadata, MediaType


class DrawIOProcessor:
    """Specialized processor for draw.io diagrams."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def can_process(self, file_name: str, media_type: str) -> bool:
        """Check if this is a draw.io file."""
        return (media_type == MediaType.DRAWIO.value or 
                file_name.lower().endswith('.drawio'))

    def is_drawio_xml(self, content_bytes: bytes) -> bool:
        """Check if XML content is a draw.io diagram."""
        try:
            content = content_bytes.decode('utf-8', errors='replace')
            return ('mxfile' in content.lower() and
                    ('mxgraph' in content.lower() or 'diagram' in content.lower()))
        except:
            return False

    def extract_text(self, content_bytes: bytes) -> ExtractionResult:
        """Extract text and metadata from draw.io diagrams."""
        try:
            content = content_bytes.decode('utf-8', errors='replace')

            # Parse XML
            root = ET.fromstring(content)

            # Extract metadata and text
            metadata = self._extract_drawio_metadata(root)
            text_content = self._format_drawio_content(metadata)

            return ExtractionResult(
                text=text_content,
                success=True,
                metadata={
                    "type": "drawio_diagram",
                    "diagram_metadata": metadata.__dict__,
                    "format": "mxfile"
                }
            )

        except Exception as e:
            return ExtractionResult(
                text="",
                success=False,
                error_message=f"Draw.io extraction error: {e}"
            )

    def _extract_drawio_metadata(self, root: ET.Element) -> DrawIOMetadata:
        """Extract comprehensive metadata from draw.io XML structure."""
        metadata = DrawIOMetadata()

        try:
            # Extract file-level metadata
            if root.tag == 'mxfile':
                metadata.author = root.get('userAgent', '')
                metadata.version = root.get('version', '')
                metadata.created = root.get('created', '')
                metadata.modified = root.get('modified', '')

            # Process diagrams/pages
            diagrams = root.findall('.//diagram')
            metadata.page_count = len(diagrams)

            for diagram in diagrams:
                # Extract page name
                page_name = diagram.get('name', f'Page {len(metadata.page_names) + 1}')
                metadata.page_names.append(page_name)

                # Decode mxGraphModel data
                mxgraph_data = diagram.text
                if mxgraph_data:
                    decoded_data = self._decode_mxgraph_data(mxgraph_data)
                    if decoded_data:
                        self._extract_diagram_elements(decoded_data, metadata)

            # Set title from first page or default
            if metadata.page_names:
                metadata.title = metadata.page_names[0]

        except Exception as e:
            self.logger.warning(f"Error extracting draw.io metadata: {e}")

        return metadata

    def _decode_mxgraph_data(self, encoded_data: str) -> Optional[ET.Element]:
        """Decode compressed/encoded mxGraphModel data."""
        try:
            # Try URL decoding first
            try:
                decoded = urllib.parse.unquote(encoded_data)
            except:
                decoded = encoded_data

            # Try base64 decoding if it looks like base64
            if self._is_base64(decoded):
                try:
                    decoded_bytes = base64.b64decode(decoded)
                    # Try decompressing if it's compressed
                    try:
                        decoded = zlib.decompress(decoded_bytes).decode('utf-8')
                    except:
                        decoded = decoded_bytes.decode('utf-8', errors='replace')
                except:
                    pass  # Use original decoded string

            # Parse as XML
            return ET.fromstring(decoded)

        except Exception as e:
            self.logger.debug(f"Failed to decode mxGraph data: {e}")
            return None

    def _is_base64(self, s: str) -> bool:
        """Check if string looks like base64."""
        try:
            return base64.b64encode(base64.b64decode(s)).decode() == s
        except:
            return False

    def _extract_diagram_elements(self, graph_root: ET.Element, metadata: DrawIOMetadata):
        """Extract text and structural elements from diagram."""
        try:
            # Find all cells (shapes, connectors, text)
            cells = graph_root.findall('.//mxCell')

            for cell in cells:
                # Count shapes and connectors
                if cell.get('vertex') == '1':
                    metadata.shape_count += 1
                elif cell.get('edge') == '1':
                    metadata.connector_count += 1

                # Extract text content
                value = cell.get('value', '')
                if value and value.strip():
                    # Decode HTML entities and clean up
                    clean_text = self._clean_diagram_text(value)
                    if clean_text:
                        metadata.text_elements.append(clean_text)

            # Extract layer information
            layers = graph_root.findall('.//mxCell[@parent="1"]')
            layer_names = set()
            for layer in layers:
                layer_name = layer.get('value', '')
                if layer_name and layer_name not in layer_names:
                    layer_names.add(layer_name)
                    metadata.layers.append(layer_name)

        except Exception as e:
            self.logger.debug(f"Error extracting diagram elements: {e}")

    def _clean_diagram_text(self, text: str) -> str:
        """Clean and normalize text from diagram elements."""
        try:
            # Remove HTML tags
            soup = BeautifulSoup(text, 'html.parser')
            clean_text = soup.get_text()

            # Normalize whitespace
            clean_text = ' '.join(clean_text.split())

            # Remove common diagram artifacts
            artifacts = ['&nbsp;', '&lt;', '&gt;', '&amp;']
            for artifact in artifacts:
                clean_text = clean_text.replace(artifact, ' ')

            return clean_text.strip()

        except:
            return text.strip()

    def _format_drawio_content(self, metadata: DrawIOMetadata) -> str:
        """Format draw.io metadata into searchable text content."""
        content_parts = []

        # Title and basic info
        if metadata.title:
            content_parts.append(f"Diagram Title: {metadata.title}")

        if metadata.description:
            content_parts.append(f"Description: {metadata.description}")

        if metadata.author:
            content_parts.append(f"Author: {metadata.author}")

        # Page information
        if metadata.page_count > 1:
            content_parts.append(f"Pages ({metadata.page_count}): {', '.join(metadata.page_names)}")
        elif metadata.page_names:
            content_parts.append(f"Page: {metadata.page_names[0]}")

        # Structural information
        if metadata.shape_count > 0 or metadata.connector_count > 0:
            content_parts.append(
                f"Diagram Structure: {metadata.shape_count} shapes, {metadata.connector_count} connectors"
            )

        # Layer information
        if metadata.layers:
            content_parts.append(f"Layers: {', '.join(metadata.layers)}")

        # Text content from diagram elements
        if metadata.text_elements:
            content_parts.append("Text Content:")
            content_parts.extend(metadata.text_elements)

        # Metadata
        if metadata.created or metadata.modified:
            dates = []
            if metadata.created:
                dates.append(f"Created: {metadata.created}")
            if metadata.modified:
                dates.append(f"Modified: {metadata.modified}")
            content_parts.append(" | ".join(dates))

        return "\n".join(content_parts)
