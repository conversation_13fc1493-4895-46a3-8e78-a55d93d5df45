#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Constants and enumerations used throughout the Confluence  client.
"""

from enum import Enum


class APIConstants:
    """Constants used throughout the API client."""
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 100
    DEFAULT_TIMEOUT = 60
    DEFAULT_RETRY_AFTER = 60
    MAX_ATTACHMENT_PAGES = 20
    ATTACHMENT_PAGE_SIZE = 50
    MAX_CONCURRENT_CALLS = 10

    # API endpoints
    SEARCH_ENDPOINT = "/wiki/rest/api/search"
    CONTENT_ENDPOINT = "/wiki/rest/api/content"

    # Content types
    JSON_CONTENT_TYPE = "application/json"

    # Expand fields
    STANDARD_EXPAND_FIELDS = [
        'body.storage', 'body.view', 'version', 'space',
        'ancestors', 'metadata.labels'
    ]
    ATTACHMENT_EXPAND_FIELDS = ['children.attachment']
    VERSION_EXPAND_FIELDS = ['version']


class AuthType(Enum):
    """Authentication types supported by the client."""
    PAT_TOKEN = "pat_token"
    API_TOKEN = "api_token"
