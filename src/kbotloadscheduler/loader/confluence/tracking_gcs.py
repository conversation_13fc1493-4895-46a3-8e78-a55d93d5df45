#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Suivi des changements pour le système RAG Confluence avec support GCS.
Extension du système de tracking pour supporter Google Cloud Storage.
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Set
import hashlib

try:
    from google.cloud import storage as gcs_storage
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False

from .models import ContentItem, AttachmentDetail
from .utils import TextProcessor
from .logging_utils import CorrelationContext
from .thread_pool_manager import get_thread_pool_manager


class ConfluenceChangeTrackerGCS:
    """Suivi des changements dans les contenus Confluence avec stockage GCS."""

    def __init__(self, bucket_name: str, base_prefix: str = "confluence_rag/tracking"):
        """Initialise le tracker avec le bucket GCS et le préfixe spécifiés."""
        if not GCS_AVAILABLE:
            raise ImportError(
                "Le module google-cloud-storage n'est pas installé. "
                "Installez-le avec 'pip install google-cloud-storage'"
            )

        self.bucket_name = bucket_name
        self.base_prefix = base_prefix
        self.logger = logging.getLogger(__name__)

        # Initialiser le client GCS
        self.client = gcs_storage.Client()
        self.bucket = self.client.bucket(bucket_name)

        # Vérifier que le bucket existe
        if not self.bucket.exists():
            self.logger.warning(f"Le bucket {bucket_name} n'existe pas. Tentative de création...")
            self.bucket.create()

    async def has_content_changed(self, content_item: ContentItem) -> bool:
        """Vérifie si un contenu a changé depuis la dernière synchronisation."""
        content_id = content_item.id
        hash_path = f"{self.base_prefix}/content_hashes/{content_id}.json"

        # Générer le hash du contenu actuel
        current_hash = self._generate_content_hash(content_item)

        # Vérifier si le hash existe dans GCS
        blob = self.bucket.blob(hash_path)
        thread_manager = get_thread_pool_manager()
        
        try:
            exists = await thread_manager.run_in_io_pool(blob.exists)
            if not exists:
                # Le contenu n'a jamais été synchronisé
                await self._save_content_hash(content_id, current_hash)
                return True

            # Charger le hash précédent
            hash_data_json = await thread_manager.run_in_io_pool(blob.download_as_text)
            previous_hash_data = json.loads(hash_data_json)
            previous_hash = previous_hash_data.get("hash", "")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement du hash précédent pour {content_id}: {e}")
            # En cas d'erreur, considérer que le contenu a changé
            return True

        # Comparer les hashs
        has_changed = current_hash != previous_hash

        # Si le contenu a changé, mettre à jour le hash
        if has_changed:
            await self._save_content_hash(content_id, current_hash)

        return has_changed

    async def has_attachment_changed(self, attachment: AttachmentDetail) -> bool:
        """Vérifie si une pièce jointe a changé depuis la dernière synchronisation."""
        attachment_id = attachment.id
        hash_path = f"{self.base_prefix}/attachment_hashes/{attachment_id}.json"

        # Générer le hash de la pièce jointe actuelle
        current_hash = self._generate_attachment_hash(attachment)

        # Vérifier si le hash existe dans GCS
        blob = self.bucket.blob(hash_path)
        thread_manager = get_thread_pool_manager()
        
        try:
            exists = await thread_manager.run_in_io_pool(blob.exists)
            if not exists:
                # La pièce jointe n'a jamais été synchronisée
                await self._save_attachment_hash(attachment_id, current_hash)
                return True

            # Charger le hash précédent
            hash_data_json = await thread_manager.run_in_io_pool(blob.download_as_text)
            previous_hash_data = json.loads(hash_data_json)
            previous_hash = previous_hash_data.get("hash", "")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement du hash précédent pour la pièce jointe {attachment_id}: {e}")
            # En cas d'erreur, considérer que la pièce jointe a changé
            return True

        # Comparer les hashs
        has_changed = current_hash != previous_hash

        # Si la pièce jointe a changé, mettre à jour le hash
        if has_changed:
            await self._save_attachment_hash(attachment_id, current_hash)

        return has_changed

    async def record_sync(self, content_items: List[ContentItem]) -> Dict[str, Any]:
        """Enregistre une synchronisation dans l'historique GCS."""
        timestamp = datetime.now().isoformat()
        sync_id = f"sync_{timestamp.replace(':', '-')}"

        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()

        # Collecter les statistiques
        stats = {
            "sync_id": sync_id,
            "timestamp": timestamp,
            "correlation_id": correlation_id or "no-correlation-id",
            "total_content_items": len(content_items),
            "total_attachments": sum(len(item.attachments) for item in content_items),
            "spaces_processed": list(set(item.space.key for item in content_items)),
            "content_types": self._count_content_types(content_items),
            "content_ids": [item.id for item in content_items]
        }

        # Enregistrer les statistiques dans GCS
        sync_path = f"{self.base_prefix}/sync_history/{sync_id}.json"
        blob = self.bucket.blob(sync_path)
        
        try:
            stats_json = json.dumps(stats, indent=2, ensure_ascii=False)
            thread_manager = get_thread_pool_manager()
            await thread_manager.run_in_io_pool(
                lambda: blob.upload_from_string(stats_json, content_type='application/json')
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de l'enregistrement de la synchronisation {sync_id}: {e}")

        return stats

    async def get_last_sync_info(self) -> Optional[Dict[str, Any]]:
        """Récupère les informations de la dernière synchronisation depuis GCS."""
        sync_prefix = f"{self.base_prefix}/sync_history/"
        
        try:
            thread_manager = get_thread_pool_manager()
            # Lister tous les blobs de synchronisation
            blobs = await thread_manager.run_in_io_pool(
                lambda: list(self.bucket.list_blobs(prefix=sync_prefix))
            )
            
            if not blobs:
                return None

            # Trier par nom (qui contient le timestamp) et prendre le plus récent
            latest_blob = sorted(blobs, key=lambda b: b.name, reverse=True)[0]
            
            # Charger les informations de synchronisation
            sync_data_json = await thread_manager.run_in_io_pool(latest_blob.download_as_text)
            return json.loads(sync_data_json)
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des informations de la dernière synchronisation: {e}")
            return None

    def _generate_content_hash(self, content_item: ContentItem) -> str:
        """Génère un hash pour un contenu."""
        # Utiliser les champs pertinents pour le hash
        hash_data = {
            "id": content_item.id,
            "title": content_item.title,
            "version": content_item.version,
            "last_updated": content_item.last_updated,
            "body_hash": TextProcessor.generate_content_hash(content_item.body_storage or "")
        }

        # Convertir en JSON et générer le hash
        hash_json = json.dumps(hash_data, sort_keys=True)
        return hashlib.sha256(hash_json.encode('utf-8')).hexdigest()

    def _generate_attachment_hash(self, attachment: AttachmentDetail) -> str:
        """Génère un hash pour une pièce jointe."""
        # Utiliser les champs pertinents pour le hash
        hash_data = {
            "id": attachment.id,
            "title": attachment.title,
            "file_name": attachment.file_name,
            "file_size": attachment.file_size,
            "media_type": attachment.media_type,
            "created": str(attachment.created)
        }

        # Convertir en JSON et générer le hash
        hash_json = json.dumps(hash_data, sort_keys=True)
        return hashlib.sha256(hash_json.encode('utf-8')).hexdigest()

    async def _save_content_hash(self, content_id: str, content_hash: str) -> None:
        """Enregistre le hash d'un contenu dans GCS."""
        hash_path = f"{self.base_prefix}/content_hashes/{content_id}.json"
        hash_data = {
            "id": content_id,
            "hash": content_hash,
            "timestamp": datetime.now().isoformat()
        }

        try:
            blob = self.bucket.blob(hash_path)
            hash_json = json.dumps(hash_data, indent=2, ensure_ascii=False)
            thread_manager = get_thread_pool_manager()
            await thread_manager.run_in_io_pool(
                lambda: blob.upload_from_string(hash_json, content_type='application/json')
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de l'enregistrement du hash pour {content_id}: {e}")

    async def _save_attachment_hash(self, attachment_id: str, attachment_hash: str) -> None:
        """Enregistre le hash d'une pièce jointe dans GCS."""
        hash_path = f"{self.base_prefix}/attachment_hashes/{attachment_id}.json"
        hash_data = {
            "id": attachment_id,
            "hash": attachment_hash,
            "timestamp": datetime.now().isoformat()
        }

        try:
            blob = self.bucket.blob(hash_path)
            hash_json = json.dumps(hash_data, indent=2, ensure_ascii=False)
            thread_manager = get_thread_pool_manager()
            await thread_manager.run_in_io_pool(
                lambda: blob.upload_from_string(hash_json, content_type='application/json')
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de l'enregistrement du hash pour la pièce jointe {attachment_id}: {e}")

    def _count_content_types(self, content_items: List[ContentItem]) -> Dict[str, int]:
        """Compte les types de contenu."""
        type_counts = {}
        for item in content_items:
            content_type = item.type
            if content_type in type_counts:
                type_counts[content_type] += 1
            else:
                type_counts[content_type] = 1
        return type_counts


def get_change_tracker(storage_type: str = "filesystem", **kwargs) -> "ConfluenceChangeTracker":
    """Factory pour créer un tracker de changements selon le type de stockage."""
    if storage_type.lower() == "filesystem":
        from .tracking import ConfluenceChangeTracker
        storage_dir = kwargs.get('storage_dir', '.confluence_rag_data')
        return ConfluenceChangeTracker(storage_dir=storage_dir)
    elif storage_type.lower() == "gcs":
        bucket_name = kwargs.get('bucket_name')
        if not bucket_name:
            raise ValueError("Le paramètre 'bucket_name' est requis pour le stockage GCS")
        base_prefix = kwargs.get('base_prefix', 'confluence_rag/tracking')
        return ConfluenceChangeTrackerGCS(bucket_name=bucket_name, base_prefix=base_prefix)
    else:
        raise ValueError(f"Type de stockage non supporté pour le tracking: {storage_type}")
