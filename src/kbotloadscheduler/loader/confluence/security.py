#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Security utilities for the Confluence loader client.
"""

import re


class SecurityUtils:
    """Utility class for security-related operations."""

    # Sensitive data patterns for sanitization
    SENSITIVE_PATTERNS = [
        # Email:token combinations (must come before general token patterns)
        (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}:[A-Za-z0-9]{16,}', '***EMAIL:TOKEN***'),

        # Authorization headers (case-insensitive, preserve original case structure)
        (r'(authorization:\s*bearer\s+)[A-Za-z0-9._-]+', r'\1***TOKEN***'),
        (r'(authorization:\s*basic\s+)[A-Za-z0-9+/=]+', r'\1***TOKEN***'),

        # JSON key-value pairs with sensitive keys
        (r'("[^"]*(?:token|password|secret|key|apikey|auth)[^"]*":\s*")[^"]*(")', r'\1***\2'),

        # URL credentials
        (r'://[^:]+:[^@]+@', '://***USER***:***PASS***@'),

        # URL parameters with sensitive keys
        (r'([?&])(token|password|secret|key|apikey|auth)=([^&\s]+)', r'\1\2=***'),

        # Special case for "tokens: X and Y" pattern - both tokens should be masked (must come before general patterns)
        (r'\b(tokens:\s*)([A-Za-z0-9]{6,})(\s+and\s+)([A-Za-z0-9]{6,})\b', r'\1***TOKEN***\3***TOKEN***'),

        # Tokens after specific keywords (20+ characters only to avoid false positives)
        (r'\b((?:token):\s*)([A-Za-z0-9]{20,})\b', r'\1***TOKEN***'),
        (r'\b(with\s+token\s+)([A-Za-z0-9]{20,})\b', r'\1***TOKEN***'),

        # Long tokens (20+ characters) - for PAT tokens
        (r'\b[A-Za-z0-9]{20,}\b', '***TOKEN***'),
    ]

    @classmethod
    def sanitize_error_message(cls, error_message: str) -> str:
        """
        Nettoie un message d'erreur en supprimant les informations sensibles.

        Args:
            error_message: Le message d'erreur original

        Returns:
            Le message d'erreur nettoyé sans informations sensibles
        """
        sanitized_message = str(error_message)
        for pattern, replacement in cls.SENSITIVE_PATTERNS:
            sanitized_message = re.sub(
                pattern, replacement, sanitized_message,
                flags=re.IGNORECASE
            )
        return sanitized_message
