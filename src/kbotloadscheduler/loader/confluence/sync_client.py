#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Client API Confluence synchrone basé sur requests.
"""

import logging
import time
from typing import List, Dict, Any, Optional, Callable, TypeVar
from urllib.parse import urljoin

from .config import ConfluenceConfig, SearchCriteria
from .models import ContentItem, AttachmentDetail
from .utils import RetryHandler
from .circuit_breaker import CircuitBreaker
from .sync_auth import SyncAuthenticationManager
from .sync_http_client import SyncHTTPClient, SyncResponseProcessor, SyncRequestContext
from .constants import APIConstants
from .security import SecurityUtils
from .exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError
)

T = TypeVar('T')


class SyncConfluenceClient:
    """Client API Confluence synchrone utilisant requests."""

    def __init__(self, config: ConfluenceConfig):
        """Initialise le client avec la configuration fournie."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.auth_manager = SyncAuthenticationManager(config)
        self.response_processor = SyncResponseProcessor(self.logger)
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_config)
        self.retry_config = config.retry_config

        # HTTP client
        effective_timeout = config.timeout if config.timeout and config.timeout > 0 else APIConstants.DEFAULT_TIMEOUT
        self.http_client = SyncHTTPClient(timeout=effective_timeout)

        # Base URL
        self.base_url = str(config.url).rstrip('/')

        # Retry handlers
        self.search_retry_handler = RetryHandler(self.retry_config)
        self.download_retry_handler = RetryHandler(self.retry_config)

    def close(self):
        """Ferme les ressources du client."""
        if hasattr(self, 'http_client'):
            self.http_client.close()

    def __enter__(self):
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        self.close()

    def _sync_retry(self, func: Callable, retry_handler: RetryHandler, *args, **kwargs):
        """Méthode de retry synchrone."""
        attempt = 1
        max_attempts = retry_handler.config.max_attempts
        last_exception = None

        while attempt <= max_attempts:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e

                # Vérifier si l'exception justifie une nouvelle tentative
                should_retry, suggested_wait = retry_handler.should_retry_exception(e)

                if not should_retry or attempt >= max_attempts:
                    # Ne pas réessayer ou limite atteinte
                    self.logger.warning(
                        f"Échec définitif après {attempt}/{max_attempts} tentatives: {str(e)}"
                    )
                    raise

                # Calculer le temps d'attente
                wait_time = suggested_wait or retry_handler.calculate_next_wait_time(attempt)

                self.logger.info(
                    f"Tentative {attempt}/{max_attempts} échouée: {str(e)}. "
                    f"Nouvelle tentative dans {wait_time:.2f} secondes."
                )

                # Attendre avant la prochaine tentative
                time.sleep(wait_time)
                attempt += 1

        # Si on arrive ici, c'est qu'on a épuisé toutes les tentatives
        if last_exception:
            raise last_exception

        # Cas improbable, mais pour satisfaire le type checker
        raise RuntimeError("Toutes les tentatives ont échoué sans exception")

    def _make_api_request(
            self,
            method: str,
            endpoint: str,
            service_name: str,
            retry_decorator: Optional[Callable] = None,
            process_response: Optional[Callable[[Any], T]] = None,
            download_mode: bool = False,
            **kwargs
    ) -> T:
        """Méthode générique pour effectuer des appels API avec gestion des erreurs."""
        context = SyncRequestContext(
            method=method,
            endpoint=endpoint,
            service_name=service_name,
            download_mode=download_mode,
            headers=kwargs.get('headers', {}),
            params=kwargs.get('params', {})
        )

        # Configure circuit breaker exceptions
        circuit_exceptions = (APIError, RateLimitExceededError)

        def make_request():
            return self._execute_request(context, **kwargs)

        # Apply circuit breaker
        circuit_breaker_func = self.circuit_breaker.call(make_request, circuit_exceptions)

        try:
            # Apply retry if provided
            if retry_decorator:
                response_data = self._sync_retry(circuit_breaker_func, retry_decorator)
            else:
                response_data = circuit_breaker_func()

            # Apply response processor if provided
            if process_response:
                return process_response(response_data)

            return response_data

        except Exception as e:
            self.logger.error(f"Erreur lors de l'appel API {service_name}: {e}")
            raise

    def _execute_request(self, context: SyncRequestContext, **kwargs) -> Any:
        """Execute the actual HTTP request."""
        # Prepare URL
        if context.endpoint.startswith('http'):
            url = context.endpoint
        else:
            url = urljoin(self.base_url, context.endpoint.lstrip('/'))

        # Get authentication
        headers, auth = self.auth_manager.get_auth_headers_and_auth()
        
        # Merge headers
        if context.headers:
            headers.update(context.headers)
        if kwargs.get('headers'):
            headers.update(kwargs['headers'])

        # Make request
        response = self.http_client.request(
            method=context.method,
            url=url,
            headers=headers,
            auth=auth,
            params=context.params or kwargs.get('params'),
            json=kwargs.get('json'),
            data=kwargs.get('data')
        )

        # Process response
        return self.response_processor.process_response(response, context)

    def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche des contenus Confluence selon les critères spécifiés."""
        self.logger.info(f"Début de la recherche de contenu avec les critères: {criteria}")
        return self._search_content_sequential(criteria)

    def _search_content_sequential(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche séquentielle des contenus."""
        # Use the built-in CQL generation method
        cql = criteria.to_cql()
        
        # Prepare expand fields
        expand_fields = APIConstants.STANDARD_EXPAND_FIELDS
        if criteria.include_attachments:
            expand_fields.extend(APIConstants.ATTACHMENT_EXPAND_FIELDS)
        expand = ','.join(list(set(expand_fields)))

        # Execute search with pagination
        content_items = []
        start = 0
        limit = min(criteria.max_results or APIConstants.DEFAULT_PAGE_SIZE, APIConstants.MAX_PAGE_SIZE)
        max_results = criteria.max_results or 0

        while True:
            params = {
                "cql": cql,
                "start": start,
                "limit": limit,
                "expand": expand
            }

            try:
                response_json: Dict[str, Any] = self._make_api_request(
                    method="GET",
                    endpoint=APIConstants.SEARCH_ENDPOINT,
                    service_name="confluence_search",
                    retry_decorator=self.search_retry_handler,
                    params=params
                )

                if not isinstance(response_json, dict) or "results" not in response_json:
                    raise APIError(f"Structure de réponse inattendue: {type(response_json)}")

                results = response_json.get("results", [])
                for result_json in results:
                    if isinstance(result_json, dict):
                        try:
                            content_item = ContentItem.from_json(result_json)
                            content_items.append(content_item)
                        except Exception as e:
                            self.logger.warning(f"Erreur lors de la conversion d'un résultat en ContentItem: {e}")

                # Check if we should continue pagination
                current_page_size = response_json.get("size", len(results))
                if current_page_size < limit or not response_json.get("_links", {}).get("next"):
                    break

                # Check max_results limit
                if max_results > 0 and len(content_items) >= max_results:
                    break

                start += current_page_size

            except Exception as e:
                self.logger.error(f"Erreur lors de la recherche: {e}")
                if isinstance(e, (APIError, RateLimitExceededError, AuthenticationError)):
                    raise
                raise APIError(f"Erreur lors de la recherche: {str(e)}") from e

        # Ensure we don't exceed max_results
        if max_results > 0 and len(content_items) > max_results:
            content_items = content_items[:max_results]

        self.logger.info(f"{len(content_items)} ContentItems récupérés.")
        return content_items

    def get_content(self, content_id: str) -> ContentItem:
        """Récupère les détails complets d'un élément de contenu Confluence par son ID."""
        self.logger.info(f"Récupération du contenu ID: {content_id}")

        expand_fields = APIConstants.STANDARD_EXPAND_FIELDS + ['children.page']
        expand = ','.join(list(set(expand_fields)))
        endpoint = f"{APIConstants.CONTENT_ENDPOINT}/{content_id}"
        params = {"expand": expand}

        try:
            content_json: Dict[str, Any] = self._make_api_request(
                method="GET",
                endpoint=endpoint,
                service_name="confluence_get_content",
                params=params
            )

            if not isinstance(content_json, dict):
                self.logger.error(
                    f"Type de réponse inattendu pour get_content ID {content_id}: "
                    f"{type(content_json)}. Attendu: dict."
                )
                raise APIError(f"Type de réponse inattendu: {type(content_json)}")

            return ContentItem.from_json(content_json)

        except ContentNotFoundError:
            self.logger.warning(f"Contenu ID {content_id} non trouvé.")
            raise
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération du contenu ID {content_id}: {e}",
                exc_info=True
            )
            if isinstance(e, (APIError, ContentNotFoundError)):
                raise
            raise APIError(
                f"Erreur lors de la récupération du contenu ID {content_id}: {str(e)}"
            ) from e

    def get_attachments(self, content_id: str) -> List[AttachmentDetail]:
        """Récupère la liste des pièces jointes pour un élément de contenu Confluence."""
        self.logger.info(f"Récupération des pièces jointes pour le contenu ID: {content_id}")

        attachments_list: List[AttachmentDetail] = []
        start = 0
        limit = getattr(
            self.config, 'attachment_page_size',
            APIConstants.ATTACHMENT_PAGE_SIZE
        )
        expand = ','.join(APIConstants.VERSION_EXPAND_FIELDS)
        endpoint = f"{APIConstants.CONTENT_ENDPOINT}/{content_id}/child/attachment"

        page_count = 0
        max_attachment_pages = getattr(self.config, 'max_attachment_pages', APIConstants.MAX_ATTACHMENT_PAGES)

        while True:
            page_count += 1
            if page_count > max_attachment_pages:
                self.logger.warning(f"Limite de pagination ({max_attachment_pages} pages) atteinte pour les pièces jointes du contenu ID {content_id}.")
                break

            params = {"start": start, "limit": limit, "expand": expand}
            try:
                response_json: Dict[str, Any] = self._make_api_request(
                    method="GET",
                    endpoint=endpoint,
                    service_name="confluence_get_attachments",
                    params=params
                )

                if not isinstance(response_json, dict) or "results" not in response_json or not isinstance(response_json["results"], list):
                    self.logger.error(f"Structure de réponse inattendue pour get_attachments (ID {content_id}, start {start}): {type(response_json)}. Attendu: dict avec 'results' list.")
                    if isinstance(response_json, dict) and response_json.get("results") == [] and response_json.get("size", 0) == 0:
                        break
                    raise APIError(f"Structure de réponse inattendue pour get_attachments: {type(response_json)}")

                attachment_results = response_json.get("results", [])
                for att_json in attachment_results:
                    if not isinstance(att_json, dict):
                        self.logger.warning(f"Item d'attachement non-dict pour content ID {content_id}: {type(att_json)}")
                        continue
                    try:
                        attachment = AttachmentDetail.from_json(att_json, content_id=content_id)
                        attachments_list.append(attachment)
                    except Exception as e:
                        self.logger.warning(f"Erreur lors de la conversion d'un résultat d'attachement en AttachmentDetail "
                                            f"pour content ID {content_id}: {e}. Attachement JSON: {str(att_json)[:200]}", exc_info=False)

                current_page_size = response_json.get("size", len(attachment_results))
                if current_page_size < limit or not response_json.get("_links", {}).get("next"):
                    break

                start += current_page_size

            except ContentNotFoundError:
                self.logger.info(f"Aucune ressource de pièce jointe (/child/attachment) trouvée pour le contenu ID {content_id}.")
                break
            except Exception as e:
                self.logger.error(f"Erreur lors de la récupération d'une page de pièces jointes pour {content_id}: {e}", exc_info=True)
                if isinstance(e, (APIError, RateLimitExceededError)):
                    raise
                break

        self.logger.info(f"{len(attachments_list)} pièces jointes récupérées pour le contenu ID: {content_id}")
        return attachments_list

    def download_attachment(self, attachment_detail: AttachmentDetail) -> bytes:
        """
        Télécharge le contenu d'une pièce jointe.
        """
        if not attachment_detail.download_url:
            self.logger.error(f"Impossible de télécharger la pièce jointe ID={attachment_detail.id}, "
                              f"Nom='{attachment_detail.file_name}': "
                              "l'attribut 'download_url' est manquant ou vide dans AttachmentDetail.")
            raise ValueError(f"AttachmentDetail (ID: {attachment_detail.id}, Nom: {attachment_detail.file_name}) "
                             "manque de 'download_url'.")

        download_url_str = str(attachment_detail.download_url)

        self.logger.info(f"Tentative de téléchargement de la pièce jointe: ID={attachment_detail.id}, "
                         f"Nom='{attachment_detail.file_name}', Link='{download_url_str}'")

        try:
            attachment_data: bytes = self._make_api_request(
                method="GET",
                endpoint=download_url_str,
                service_name="confluence_attachment_download",
                download_mode=True,
                retry_decorator=self.download_retry_handler
            )
            self.logger.info(f"Pièce jointe ID={attachment_detail.id} ('{attachment_detail.file_name}') "
                             f"téléchargée avec succès ({len(attachment_data)} octets).")
            return attachment_data
        except ContentNotFoundError:
            self.logger.error(f"Pièce jointe non trouvée (404): ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}') au lien '{download_url_str}'")
            raise
        except APIError as e:
            self.logger.error(f"Erreur API lors du téléchargement de la pièce jointe ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}'): {e}")
            raise
        except Exception as e:
            sanitized_error = SecurityUtils.sanitize_error_message(str(e))
            self.logger.error(f"Erreur inattendue lors du téléchargement de la pièce jointe ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}'): {sanitized_error}", exc_info=True)
            raise APIError(f"Erreur inattendue de téléchargement: {sanitized_error}") from e
