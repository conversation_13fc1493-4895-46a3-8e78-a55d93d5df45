#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour les énumérations du package processing.
"""

import unittest
from dataclasses import fields

from ..processing.enums import (
    ProcessingStatus, MediaType, ExtractionResult, DrawIOMetadata
)


class TestProcessingStatus(unittest.TestCase):
    """Tests pour l'énumération ProcessingStatus."""

    def test_processing_status_values(self):
        """Test des valeurs de l'énumération ProcessingStatus."""
        self.assertEqual(ProcessingStatus.PENDING.value, "pending")
        self.assertEqual(ProcessingStatus.PROCESSING.value, "processing")
        self.assertEqual(ProcessingStatus.COMPLETED.value, "completed")
        self.assertEqual(ProcessingStatus.FAILED.value, "failed")

    def test_processing_status_members(self):
        """Test des membres de l'énumération ProcessingStatus."""
        statuses = list(ProcessingStatus)
        self.assertEqual(len(statuses), 4)
        self.assertIn(ProcessingStatus.PENDING, statuses)
        self.assertIn(ProcessingStatus.PROCESSING, statuses)
        self.assertIn(ProcessingStatus.COMPLETED, statuses)
        self.assertIn(ProcessingStatus.FAILED, statuses)

    def test_processing_status_string_representation(self):
        """Test de la représentation en chaîne des statuts."""
        self.assertEqual(str(ProcessingStatus.PENDING), "ProcessingStatus.PENDING")
        self.assertEqual(ProcessingStatus.PENDING.name, "PENDING")

    def test_processing_status_comparison(self):
        """Test de comparaison des statuts."""
        self.assertEqual(ProcessingStatus.PENDING, ProcessingStatus.PENDING)
        self.assertNotEqual(ProcessingStatus.PENDING, ProcessingStatus.COMPLETED)

    def test_processing_status_iteration(self):
        """Test d'itération sur les statuts."""
        status_values = [status.value for status in ProcessingStatus]
        expected_values = ["pending", "processing", "completed", "failed"]
        self.assertEqual(status_values, expected_values)


class TestMediaType(unittest.TestCase):
    """Tests pour l'énumération MediaType."""

    def test_media_type_values(self):
        """Test des valeurs de l'énumération MediaType."""
        self.assertEqual(MediaType.PDF.value, "application/pdf")
        self.assertEqual(MediaType.DOCX.value, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
        self.assertEqual(MediaType.XLSX.value, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        self.assertEqual(MediaType.TXT.value, "text/plain")
        self.assertEqual(MediaType.HTML.value, "text/html")
        self.assertEqual(MediaType.IMAGE.value, "image/")
        self.assertEqual(MediaType.DRAWIO.value, "application/vnd.jgraph.mxfile")
        self.assertEqual(MediaType.XML.value, "application/xml")

    def test_media_type_members(self):
        """Test des membres de l'énumération MediaType."""
        media_types = list(MediaType)
        self.assertEqual(len(media_types), 8)
        
        expected_types = [
            MediaType.PDF, MediaType.DOCX, MediaType.XLSX, MediaType.TXT,
            MediaType.HTML, MediaType.IMAGE, MediaType.DRAWIO, MediaType.XML
        ]
        
        for media_type in expected_types:
            self.assertIn(media_type, media_types)

    def test_media_type_document_formats(self):
        """Test des formats de documents supportés."""
        document_formats = [MediaType.PDF, MediaType.DOCX, MediaType.XLSX, MediaType.TXT]
        
        for format_type in document_formats:
            self.assertIsInstance(format_type.value, str)
            self.assertTrue(format_type.value.startswith("application/") or format_type.value.startswith("text/"))

    def test_media_type_special_formats(self):
        """Test des formats spéciaux."""
        # Test du format image (préfixe)
        self.assertTrue(MediaType.IMAGE.value.endswith("/"))
        
        # Test du format Draw.io
        self.assertIn("mxfile", MediaType.DRAWIO.value)

    def test_media_type_by_value_lookup(self):
        """Test de recherche par valeur."""
        # Créer un dictionnaire de lookup
        value_to_type = {media_type.value: media_type for media_type in MediaType}
        
        self.assertEqual(value_to_type["application/pdf"], MediaType.PDF)
        self.assertEqual(value_to_type["text/plain"], MediaType.TXT)
        self.assertEqual(value_to_type["application/vnd.jgraph.mxfile"], MediaType.DRAWIO)

    def test_media_type_string_representation(self):
        """Test de la représentation en chaîne des types de média."""
        self.assertEqual(str(MediaType.PDF), "MediaType.PDF")
        self.assertEqual(MediaType.PDF.name, "PDF")


class TestExtractionResult(unittest.TestCase):
    """Tests pour la classe ExtractionResult."""

    def test_extraction_result_creation_minimal(self):
        """Test de création d'ExtractionResult avec paramètres minimaux."""
        result = ExtractionResult(text="Test text", success=True)
        
        self.assertEqual(result.text, "Test text")
        self.assertTrue(result.success)
        self.assertIsNone(result.error_message)
        self.assertIsNone(result.metadata)

    def test_extraction_result_creation_complete(self):
        """Test de création d'ExtractionResult avec tous les paramètres."""
        metadata = {"page_count": 5, "format": "pdf"}
        result = ExtractionResult(
            text="Extracted text",
            success=True,
            error_message=None,
            metadata=metadata
        )
        
        self.assertEqual(result.text, "Extracted text")
        self.assertTrue(result.success)
        self.assertIsNone(result.error_message)
        self.assertEqual(result.metadata, metadata)

    def test_extraction_result_failure(self):
        """Test de création d'ExtractionResult pour un échec."""
        result = ExtractionResult(
            text="",
            success=False,
            error_message="Failed to extract text"
        )
        
        self.assertEqual(result.text, "")
        self.assertFalse(result.success)
        self.assertEqual(result.error_message, "Failed to extract text")
        self.assertIsNone(result.metadata)

    def test_extraction_result_fields(self):
        """Test des champs de la dataclass ExtractionResult."""
        result_fields = [field.name for field in fields(ExtractionResult)]
        expected_fields = ["text", "success", "error_message", "metadata"]
        
        self.assertEqual(result_fields, expected_fields)

    def test_extraction_result_equality(self):
        """Test d'égalité entre instances ExtractionResult."""
        result1 = ExtractionResult(text="Test", success=True)
        result2 = ExtractionResult(text="Test", success=True)
        result3 = ExtractionResult(text="Different", success=True)
        
        self.assertEqual(result1, result2)
        self.assertNotEqual(result1, result3)

    def test_extraction_result_repr(self):
        """Test de la représentation string d'ExtractionResult."""
        result = ExtractionResult(text="Test", success=True)
        repr_str = repr(result)
        
        self.assertIn("ExtractionResult", repr_str)
        self.assertIn("text='Test'", repr_str)
        self.assertIn("success=True", repr_str)


class TestDrawIOMetadata(unittest.TestCase):
    """Tests pour la classe DrawIOMetadata."""

    def test_drawio_metadata_creation_default(self):
        """Test de création de DrawIOMetadata avec valeurs par défaut."""
        metadata = DrawIOMetadata()
        
        self.assertEqual(metadata.title, "")
        self.assertEqual(metadata.description, "")
        self.assertEqual(metadata.author, "")
        self.assertEqual(metadata.created, "")
        self.assertEqual(metadata.modified, "")
        self.assertEqual(metadata.version, "")
        self.assertEqual(metadata.page_count, 0)
        self.assertEqual(metadata.page_names, [])
        self.assertEqual(metadata.text_elements, [])
        self.assertEqual(metadata.shape_count, 0)
        self.assertEqual(metadata.connector_count, 0)
        self.assertEqual(metadata.layers, [])

    def test_drawio_metadata_creation_with_values(self):
        """Test de création de DrawIOMetadata avec valeurs spécifiées."""
        page_names = ["Page 1", "Page 2"]
        text_elements = ["Title", "Description"]
        layers = ["Layer 1", "Background"]
        
        metadata = DrawIOMetadata(
            title="Test Diagram",
            description="A test diagram",
            author="Test Author",
            created="2023-01-01",
            modified="2023-01-02",
            version="1.0",
            page_count=2,
            page_names=page_names,
            text_elements=text_elements,
            shape_count=10,
            connector_count=5,
            layers=layers
        )
        
        self.assertEqual(metadata.title, "Test Diagram")
        self.assertEqual(metadata.description, "A test diagram")
        self.assertEqual(metadata.author, "Test Author")
        self.assertEqual(metadata.created, "2023-01-01")
        self.assertEqual(metadata.modified, "2023-01-02")
        self.assertEqual(metadata.version, "1.0")
        self.assertEqual(metadata.page_count, 2)
        self.assertEqual(metadata.page_names, page_names)
        self.assertEqual(metadata.text_elements, text_elements)
        self.assertEqual(metadata.shape_count, 10)
        self.assertEqual(metadata.connector_count, 5)
        self.assertEqual(metadata.layers, layers)

    def test_drawio_metadata_post_init(self):
        """Test de la méthode __post_init__ de DrawIOMetadata."""
        # Test avec None pour les listes
        metadata = DrawIOMetadata(
            title="Test",
            page_names=None,
            text_elements=None,
            layers=None
        )
        
        # Les listes None doivent être initialisées comme listes vides
        self.assertEqual(metadata.page_names, [])
        self.assertEqual(metadata.text_elements, [])
        self.assertEqual(metadata.layers, [])

    def test_drawio_metadata_fields(self):
        """Test des champs de la dataclass DrawIOMetadata."""
        metadata_fields = [field.name for field in fields(DrawIOMetadata)]
        expected_fields = [
            "title", "description", "author", "created", "modified", "version",
            "page_count", "page_names", "text_elements", "shape_count",
            "connector_count", "layers"
        ]
        
        self.assertEqual(metadata_fields, expected_fields)

    def test_drawio_metadata_list_modification(self):
        """Test de modification des listes dans DrawIOMetadata."""
        metadata = DrawIOMetadata()
        
        # Ajouter des éléments aux listes
        metadata.page_names.append("New Page")
        metadata.text_elements.append("New Text")
        metadata.layers.append("New Layer")
        
        self.assertEqual(len(metadata.page_names), 1)
        self.assertEqual(len(metadata.text_elements), 1)
        self.assertEqual(len(metadata.layers), 1)
        self.assertEqual(metadata.page_names[0], "New Page")

    def test_drawio_metadata_equality(self):
        """Test d'égalité entre instances DrawIOMetadata."""
        metadata1 = DrawIOMetadata(title="Test", page_count=1)
        metadata2 = DrawIOMetadata(title="Test", page_count=1)
        metadata3 = DrawIOMetadata(title="Different", page_count=1)
        
        self.assertEqual(metadata1, metadata2)
        self.assertNotEqual(metadata1, metadata3)

    def test_drawio_metadata_repr(self):
        """Test de la représentation string de DrawIOMetadata."""
        metadata = DrawIOMetadata(title="Test Diagram", page_count=2)
        repr_str = repr(metadata)
        
        self.assertIn("DrawIOMetadata", repr_str)
        self.assertIn("title='Test Diagram'", repr_str)
        self.assertIn("page_count=2", repr_str)


if __name__ == '__main__':
    unittest.main()
