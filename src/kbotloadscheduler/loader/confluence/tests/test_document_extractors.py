#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour les extracteurs de documents.
"""

import unittest
import logging
import io
from unittest.mock import Mock, patch, MagicMock

from ..processing.document_extractors import (
    DocumentExtractor, ExtractorConfig, BaseExtractor,
    PDFExtractor, DOCXExtractor, XLSXExtractor, PlainTextExtractor,
    HTMLExtractor, XMLExtractor
)
from ..processing.enums import ExtractionResult, MediaType


class TestExtractorConfig(unittest.TestCase):
    """Tests pour la classe ExtractorConfig."""

    def test_extractor_config_defaults(self):
        """Test des valeurs par défaut de ExtractorConfig."""
        config = ExtractorConfig()

        self.assertEqual(config.max_file_size, 100 * 1024 * 1024)  # 100MB
        self.assertEqual(config.max_pdf_pages, 1000)
        self.assertEqual(config.max_xlsx_rows_per_sheet, 10000)
        self.assertEqual(config.encoding_fallbacks, ('utf-8', 'latin-1', 'cp1252'))
        self.assertTrue(config.strip_whitespace)
        self.assertTrue(config.include_metadata)

    def test_extractor_config_custom_values(self):
        """Test de création avec valeurs personnalisées."""
        config = ExtractorConfig(
            max_file_size=50 * 1024 * 1024,
            max_pdf_pages=500,
            strip_whitespace=False,
            include_metadata=False
        )

        self.assertEqual(config.max_file_size, 50 * 1024 * 1024)
        self.assertEqual(config.max_pdf_pages, 500)
        self.assertFalse(config.strip_whitespace)
        self.assertFalse(config.include_metadata)

    def test_extractor_config_frozen(self):
        """Test que ExtractorConfig est immutable."""
        config = ExtractorConfig()

        with self.assertRaises(Exception):  # dataclass frozen
            config.max_file_size = 1000


class TestBaseExtractor(unittest.TestCase):
    """Tests pour la classe BaseExtractor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.config = ExtractorConfig()

    def test_base_extractor_is_abstract(self):
        """Test que BaseExtractor ne peut pas être instanciée directement."""
        with self.assertRaises(TypeError):
            BaseExtractor(self.logger, self.config)

    def test_validate_file_size_within_limit(self):
        """Test de validation de taille de fichier dans les limites."""
        # Créer une classe concrète pour tester
        class TestExtractor(BaseExtractor):
            def can_handle(self, media_type, extension):
                return True
            def extract(self, file_obj, file_name, content_bytes):
                return ExtractionResult("test", True)

        extractor = TestExtractor(self.logger, self.config)
        content = b"small content"

        result = extractor._validate_file_size(content, "test.txt")
        self.assertIsNone(result)  # Pas d'erreur

    def test_validate_file_size_exceeds_limit(self):
        """Test de validation de taille de fichier dépassant les limites."""
        class TestExtractor(BaseExtractor):
            def can_handle(self, media_type, extension):
                return True
            def extract(self, file_obj, file_name, content_bytes):
                return ExtractionResult("test", True)

        small_config = ExtractorConfig(max_file_size=10)  # 10 bytes max
        extractor = TestExtractor(self.logger, small_config)
        content = b"this content is too large"

        result = extractor._validate_file_size(content, "large.txt")

        self.assertIsNotNone(result)
        self.assertFalse(result.success)
        self.assertIn("exceeds maximum size", result.error_message)


class TestPDFExtractor(unittest.TestCase):
    """Tests pour la classe PDFExtractor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.config = ExtractorConfig()
        self.extractor = PDFExtractor(self.logger, self.config)

    def test_can_handle_pdf_media_type(self):
        """Test de reconnaissance du type MIME PDF."""
        self.assertTrue(self.extractor.can_handle("application/pdf", ".txt"))
        self.assertFalse(self.extractor.can_handle("text/plain", ".txt"))

    def test_can_handle_pdf_extension(self):
        """Test de reconnaissance de l'extension PDF."""
        self.assertTrue(self.extractor.can_handle("unknown/type", ".pdf"))
        self.assertFalse(self.extractor.can_handle("unknown/type", ".txt"))

    @patch('confluence_rag.processing.document_extractors.pypdf')
    def test_extract_pdf_success(self, mock_pypdf2):
        """Test d'extraction PDF réussie."""
        # Configuration du mock
        mock_reader = Mock()
        mock_page = Mock()
        mock_page.extract_text.return_value = "Test PDF content"
        mock_reader.pages = [mock_page]
        mock_pypdf2.PdfReader.return_value = mock_reader

        content = b"fake pdf content"
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.pdf", content)

        self.assertTrue(result.success)
        self.assertIn("Test PDF content", result.text)
        self.assertIn("total_pages", result.metadata)
        self.assertEqual(result.metadata["total_pages"], 1)

    @patch('confluence_rag.processing.document_extractors.pypdf')
    def test_extract_pdf_error(self, mock_pypdf2):
        """Test d'extraction PDF avec erreur."""
        mock_pypdf2.PdfReader.side_effect = Exception("PDF parsing error")

        content = b"invalid pdf content"
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.pdf", content)

        self.assertFalse(result.success)
        self.assertIn("PDF parsing error", result.error_message)


class TestDOCXExtractor(unittest.TestCase):
    """Tests pour la classe DOCXExtractor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.config = ExtractorConfig()
        self.extractor = DOCXExtractor(self.logger, self.config)

    def test_can_handle_docx_media_type(self):
        """Test de reconnaissance du type MIME DOCX."""
        docx_mime = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        self.assertTrue(self.extractor.can_handle(docx_mime, ".txt"))
        self.assertFalse(self.extractor.can_handle("text/plain", ".txt"))

    def test_can_handle_docx_extension(self):
        """Test de reconnaissance de l'extension DOCX."""
        self.assertTrue(self.extractor.can_handle("unknown/type", ".docx"))
        self.assertFalse(self.extractor.can_handle("unknown/type", ".txt"))

    @patch('confluence_rag.processing.document_extractors.Document')
    def test_extract_docx_success(self, mock_document_class):
        """Test d'extraction DOCX réussie."""
        # Configuration du mock
        mock_doc = Mock()
        mock_paragraph = Mock()
        mock_paragraph.text = "Test paragraph"
        mock_doc.paragraphs = [mock_paragraph]
        # Mock pour les tables vides
        mock_doc.tables = []
        mock_document_class.return_value = mock_doc

        content = b"fake docx content"
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.docx", content)

        self.assertTrue(result.success)
        self.assertIn("Test paragraph", result.text)

    @patch('confluence_rag.processing.document_extractors.Document')
    def test_extract_docx_error(self, mock_document_class):
        """Test d'extraction DOCX avec erreur."""
        mock_document_class.side_effect = Exception("DOCX parsing error")

        content = b"invalid docx content"
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.docx", content)

        self.assertFalse(result.success)
        self.assertIn("DOCX parsing error", result.error_message)


class TestPlainTextExtractor(unittest.TestCase):
    """Tests pour la classe PlainTextExtractor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.config = ExtractorConfig()
        self.extractor = PlainTextExtractor(self.logger, self.config)

    def test_can_handle_text_media_type(self):
        """Test de reconnaissance du type MIME texte."""
        self.assertTrue(self.extractor.can_handle("text/plain", ".doc"))
        self.assertFalse(self.extractor.can_handle("application/pdf", ".doc"))

    def test_can_handle_text_extension(self):
        """Test de reconnaissance de l'extension texte."""
        self.assertTrue(self.extractor.can_handle("unknown/type", ".txt"))
        self.assertFalse(self.extractor.can_handle("unknown/type", ".pdf"))

    def test_extract_text_utf8(self):
        """Test d'extraction de texte UTF-8."""
        content = "Hello, world! 🌍".encode('utf-8')
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.txt", content)

        self.assertTrue(result.success)
        self.assertEqual(result.text, "Hello, world! 🌍")
        self.assertEqual(result.metadata["encoding"], "utf-8")

    def test_extract_text_encoding_fallback(self):
        """Test d'extraction avec fallback d'encodage."""
        # Créer du contenu avec un encodage spécifique
        content = "Café français".encode('latin-1')
        file_obj = io.BytesIO(content)

        result = self.extractor.extract(file_obj, "test.txt", content)

        self.assertTrue(result.success)
        self.assertIn("Café français", result.text)

    def test_extract_text_encoding_failure(self):
        """Test d'extraction avec échec d'encodage."""
        # Créer du contenu binaire invalide
        content = b'\xff\xfe\x00\x00invalid'
        file_obj = io.BytesIO(content)

        # Mock pour forcer l'échec de tous les encodages
        with patch.object(self.extractor, '_decode_with_fallback', return_value=(None, None)):
            result = self.extractor.extract(file_obj, "test.txt", content)

            self.assertFalse(result.success)
            self.assertIn("Could not decode", result.error_message)


class TestDocumentExtractor(unittest.TestCase):
    """Tests pour la classe DocumentExtractor principale."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.extractor = DocumentExtractor(self.logger)

    def test_document_extractor_initialization(self):
        """Test de l'initialisation de DocumentExtractor."""
        self.assertIsNotNone(self.extractor.logger)
        self.assertIsNotNone(self.extractor.config)
        self.assertIsNotNone(self.extractor.drawio_processor)
        self.assertGreater(len(self.extractor._extractors), 0)

    def test_can_process_supported_formats(self):
        """Test de reconnaissance des formats supportés."""
        test_cases = [
            ("document.pdf", "application/pdf", True),
            ("document.docx", MediaType.DOCX.value, True),
            ("spreadsheet.xlsx", MediaType.XLSX.value, True),
            ("text.txt", "text/plain", True),
            ("page.html", "text/html", True),
            ("data.xml", "application/xml", True),
            ("diagram.drawio", MediaType.DRAWIO.value, True),
            ("image.png", "image/png", True),
            ("unknown.xyz", "application/unknown", False),
        ]

        for file_name, media_type, expected in test_cases:
            with self.subTest(file_name=file_name, media_type=media_type):
                result = self.extractor.can_process(file_name, media_type)
                self.assertEqual(result, expected)

    def test_extract_text_empty_content(self):
        """Test d'extraction avec contenu vide."""
        result = self.extractor.extract_text(b"", "test.txt", "text/plain")

        self.assertFalse(result.success)
        self.assertIn("Empty file content", result.error_message)

    @patch('confluence_rag.processing.document_extractors.PDFExtractor')
    def test_extract_text_pdf_delegation(self, mock_pdf_extractor_class):
        """Test de délégation vers l'extracteur PDF."""
        # Configuration du mock
        mock_extractor = Mock()
        mock_result = ExtractionResult("PDF content", True)
        mock_extractor.extract.return_value = mock_result
        mock_extractor.can_handle.return_value = True
        mock_pdf_extractor_class.return_value = mock_extractor

        # Recréer l'extracteur avec le mock
        extractor = DocumentExtractor(self.logger)
        extractor._extractors = [mock_extractor]

        content = b"fake pdf content"
        result = extractor.extract_text(content, "test.pdf", "application/pdf")

        self.assertTrue(result.success)
        self.assertEqual(result.text, "PDF content")
        mock_extractor.extract.assert_called_once()

    def test_extract_text_image_handling(self):
        """Test de gestion des images."""
        content = b"fake image content"
        result = self.extractor.extract_text(content, "image.png", "image/png")

        self.assertTrue(result.success)
        self.assertIn("[Image: image.png]", result.text)
        self.assertEqual(result.metadata["type"], "image")

    @patch('confluence_rag.processing.document_extractors.DrawIOProcessor')
    def test_extract_text_drawio_delegation(self, mock_drawio_processor_class):
        """Test de délégation vers le processeur Draw.io."""
        # Configuration du mock
        mock_processor = Mock()
        mock_result = ExtractionResult("Draw.io content", True)
        mock_processor.extract_text.return_value = mock_result
        mock_drawio_processor_class.return_value = mock_processor

        # Recréer l'extracteur avec le mock
        extractor = DocumentExtractor(self.logger)
        extractor.drawio_processor = mock_processor

        content = b"fake drawio content"
        result = extractor.extract_text(content, "diagram.drawio", MediaType.DRAWIO.value)

        self.assertTrue(result.success)
        self.assertEqual(result.text, "Draw.io content")

    def test_extract_text_unsupported_format(self):
        """Test d'extraction avec format non supporté."""
        content = b"unknown content"
        result = self.extractor.extract_text(content, "unknown.xyz", "application/unknown")

        self.assertFalse(result.success)
        self.assertIn("Unsupported media type", result.error_message)

    def test_extract_text_exception_handling(self):
        """Test de gestion des exceptions lors de l'extraction."""
        # Mock pour lever une exception
        with patch.object(self.extractor, '_extractors', []):
            with patch('confluence_rag.processing.document_extractors.Path') as mock_path:
                mock_path.side_effect = Exception("Path error")

                content = b"test content"
                result = self.extractor.extract_text(content, "test.txt", "text/plain")

                self.assertFalse(result.success)
                self.assertIn("Path error", result.error_message)


if __name__ == '__main__':
    unittest.main()
