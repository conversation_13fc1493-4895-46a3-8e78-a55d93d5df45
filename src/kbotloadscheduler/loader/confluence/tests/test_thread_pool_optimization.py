#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour les optimisations de gestion des threads.
"""

import pytest
import asyncio
import time
import threading
from unittest.mock import MagicMock, patch

from ..config import ThreadPoolConfig, ProcessingConfig
from ..thread_pool_manager import (
    ThreadPoolManager, get_thread_pool_manager, shutdown_global_thread_pools,
    thread_pool_executor
)

# Helper functions for TestThreadPoolExecutorDecorator, moved to module level
@thread_pool_executor(pool_type='io')
def _helper_io_function_decorated(value):
    return f"IO: {value}"

@thread_pool_executor(pool_type='document')
def _helper_document_function_decorated(value):
    return f"Document: {value}"

@thread_pool_executor(pool_type='api')
def _helper_api_function_decorated(value):
    return f"API: {value}"

# This helper function is designed to test the invalid pool type scenario.
# The ValueError is expected to be raised when this function is called.
@thread_pool_executor(pool_type='invalid')
def _helper_invalid_pool_function_decorated(value):
    return value


class TestThreadPoolConfig:
    """Tests pour la configuration des pools de threads."""

    def test_default_config(self):
        """Test de la configuration par défaut."""
        config = ThreadPoolConfig()

        assert config.io_thread_workers == 8
        assert config.document_processing_workers == 4
        assert config.api_thread_workers == 3
        assert config.thread_name_prefix == "ConfluenceRAG-Worker"
        assert config.max_queue_size == 100

    def test_config_from_env(self):
        """Test de la configuration depuis les variables d'environnement."""
        with patch.dict('os.environ', {
            'IO_THREAD_WORKERS': '12',
            'DOCUMENT_PROCESSING_WORKERS': '6',
            'API_THREAD_WORKERS': '4',
            'THREAD_NAME_PREFIX': 'TestRAG',
            'MAX_QUEUE_SIZE': '200'
        }):
            config = ThreadPoolConfig.from_env()

            assert config.io_thread_workers == 12
            assert config.document_processing_workers == 6
            assert config.api_thread_workers == 4
            assert config.thread_name_prefix == "TestRAG"
            assert config.max_queue_size == 200


class TestThreadPoolManager:
    """Tests pour le gestionnaire de pools de threads."""

    def setup_method(self):
        """Configuration avant chaque test."""
        # Réinitialiser l'instance singleton
        ThreadPoolManager._instance = None
        shutdown_global_thread_pools()

    def teardown_method(self):
        """Nettoyage après chaque test."""
        shutdown_global_thread_pools()

    def test_singleton_pattern(self):
        """Test du pattern Singleton."""
        config = ThreadPoolConfig(io_thread_workers=4)

        manager1 = ThreadPoolManager(config)
        manager2 = ThreadPoolManager()

        assert manager1 is manager2

    def test_pool_creation(self):
        """Test de la création des pools."""
        config = ThreadPoolConfig(
            io_thread_workers=2,
            document_processing_workers=2,
            api_thread_workers=2
        )

        manager = ThreadPoolManager(config)

        # Vérifier que tous les pools sont créés
        assert 'io' in manager._pools
        assert 'document' in manager._pools
        assert 'api' in manager._pools

        # Vérifier la configuration des pools
        io_pool = manager.get_pool('io')
        assert io_pool._max_workers == 2

    def test_get_pool_invalid_type(self):
        """Test de récupération d'un pool avec un type invalide."""
        manager = ThreadPoolManager()

        with pytest.raises(ValueError, match="Type de pool inconnu"):
            manager.get_pool('invalid_pool')

    @pytest.mark.asyncio
    async def test_run_in_io_pool(self):
        """Test d'exécution dans le pool I/O."""
        manager = ThreadPoolManager()

        def io_task(value):
            return f"IO: {value}"

        result = await manager.run_in_io_pool(io_task, "test")
        assert result == "IO: test"

    @pytest.mark.asyncio
    async def test_run_in_document_pool(self):
        """Test d'exécution dans le pool de traitement de documents."""
        manager = ThreadPoolManager()

        def document_task(value):
            return f"Document: {value}"

        result = await manager.run_in_document_pool(document_task, "test")
        assert result == "Document: test"

    @pytest.mark.asyncio
    async def test_run_in_api_pool(self):
        """Test d'exécution dans le pool API."""
        manager = ThreadPoolManager()

        def api_task(value):
            return f"API: {value}"

        result = await manager.run_in_api_pool(api_task, "test")
        assert result == "API: test"

    @pytest.mark.asyncio
    async def test_parallel_execution(self):
        """Test d'exécution parallèle dans différents pools."""
        manager = ThreadPoolManager()

        def slow_task(pool_name, duration):
            time.sleep(duration)
            return f"{pool_name}: completed"

        start_time = time.time()

        # Exécuter des tâches en parallèle dans différents pools
        results = await asyncio.gather(
            manager.run_in_io_pool(slow_task, "IO", 0.1),
            manager.run_in_document_pool(slow_task, "Document", 0.1),
            manager.run_in_api_pool(slow_task, "API", 0.1)
        )

        execution_time = time.time() - start_time

        # Vérifier que l'exécution parallèle est plus rapide que séquentielle
        assert execution_time < 0.25  # Moins que 3 * 0.1s
        assert len(results) == 3
        assert "IO: completed" in results
        assert "Document: completed" in results
        assert "API: completed" in results

    def test_submit_to_pool(self):
        """Test de soumission de tâche avec Future."""
        manager = ThreadPoolManager()

        def simple_task(value):
            return value * 2

        future = manager.submit_to_pool('io', simple_task, 5)
        result = future.result(timeout=1.0)

        assert result == 10

    def test_get_pool_stats(self):
        """Test de récupération des statistiques des pools."""
        manager = ThreadPoolManager()

        stats = manager.get_pool_stats()

        assert 'io' in stats
        assert 'document' in stats
        assert 'api' in stats

        for pool_name, pool_stats in stats.items():
            assert 'max_workers' in pool_stats
            assert 'active_threads' in pool_stats
            assert 'queue_size' in pool_stats

    def test_shutdown(self):
        """Test de fermeture des pools."""
        manager = ThreadPoolManager()

        # Vérifier que les pools sont créés
        assert len(manager._pools) == 3

        # Fermer les pools
        manager.shutdown()

        # Vérifier que les pools sont fermés
        assert manager._shutdown is True
        assert len(manager._pools) == 0

    def test_context_manager(self):
        """Test de l'utilisation comme gestionnaire de contexte."""
        with ThreadPoolManager() as manager:
            assert len(manager._pools) == 3
            assert not manager._shutdown

        # Après la sortie du contexte, les pools doivent être fermés
        assert manager._shutdown


class TestThreadPoolExecutorDecorator:
    """Tests pour le décorateur thread_pool_executor."""

    def setup_method(self):
        """Configuration avant chaque test."""
        # S'assurer qu'il y a un gestionnaire disponible
        ThreadPoolManager._instance = None
        get_thread_pool_manager()  # Initialise le gestionnaire global

    def teardown_method(self):
        """Nettoyage après chaque test."""
        shutdown_global_thread_pools() # Ensure clean state for singleton if reused
        ThreadPoolManager._instance = None # Explicitly reset for these tests too

    @pytest.mark.asyncio
    async def test_io_decorator(self):
        """Test du décorateur pour le pool I/O."""
        # noinspection PyTypeChecker
        result = await _helper_io_function_decorated("test")
        assert result == "IO: test"

    @pytest.mark.asyncio
    async def test_document_decorator(self):
        """Test du décorateur pour le pool de documents."""
        # noinspection PyTypeChecker
        result = await _helper_document_function_decorated("test")
        assert result == "Document: test"

    @pytest.mark.asyncio
    async def test_api_decorator(self):
        """Test du décorateur pour le pool API."""
        # noinspection PyTypeChecker
        result = await _helper_api_function_decorated("test")
        assert result == "API: test"

    @pytest.mark.asyncio
    async def test_invalid_pool_type(self):
        """Test du décorateur avec un type de pool invalide."""
        with pytest.raises(ValueError, match="Type de pool inconnu"):
            # noinspection PyTypeChecker
            await _helper_invalid_pool_function_decorated("test")


class TestGlobalThreadPoolManager:
    """Tests pour le gestionnaire global de pools de threads."""

    def teardown_method(self):
        """Nettoyage après chaque test."""
        shutdown_global_thread_pools()
        ThreadPoolManager._instance = None # Ensure singleton is reset

    def test_get_global_manager(self):
        """Test de récupération du gestionnaire global."""
        config = ThreadPoolConfig(io_thread_workers=4)

        manager1 = get_thread_pool_manager(config)
        manager2 = get_thread_pool_manager()

        assert manager1 is manager2

    def test_shutdown_global_pools(self):
        """Test de fermeture des pools globaux."""
        manager = get_thread_pool_manager()
        assert not manager._shutdown

        shutdown_global_thread_pools()
        # If get_thread_pool_manager might reinitialize if _instance is None after shutdown,
        # we need to ensure we are checking the state of the *same* instance.
        # The current shutdown_global_thread_pools likely calls manager.shutdown().
        assert manager._shutdown


class TestPerformanceOptimization:
    """Tests de performance pour valider les optimisations."""

    def setup_method(self):
        """Configuration avant chaque test."""
        # Réinitialiser l'instance singleton
        ThreadPoolManager._instance = None
        shutdown_global_thread_pools()

    def teardown_method(self):
        """Nettoyage après chaque test."""
        shutdown_global_thread_pools()

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_thread_reuse_performance(self):
        """Test de performance de la réutilisation des threads."""
        manager = ThreadPoolManager() # Uses default config or existing global

        def simple_task(value):
            return threading.current_thread().name

        # Exécuter plusieurs tâches et vérifier la réutilisation des threads
        tasks = [
            manager.run_in_io_pool(simple_task, i)
            for i in range(10)
        ]

        start_time = time.time()
        thread_names = await asyncio.gather(*tasks)
        execution_time = time.time() - start_time

        # Vérifier que les threads sont réutilisés
        unique_threads = set(thread_names)
        # Max workers for io_pool from default config is 8
        assert len(unique_threads) <= manager.config.io_thread_workers

        # Vérifier que l'exécution est rapide (pas de création de nouveaux threads)
        assert execution_time < 1.0

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_concurrent_pool_usage(self):
        """Test d'utilisation concurrente de différents pools."""
        manager = ThreadPoolManager() # Uses default config or existing global

        def cpu_intensive_task(pool_name, iterations):
            # Simulation d'une tâche CPU intensive
            total = 0
            for i in range(iterations):
                total += i
            return f"{pool_name}: {total}"

        start_time = time.time()

        # Exécuter des tâches concurrentes dans différents pools
        results = await asyncio.gather(
            manager.run_in_io_pool(cpu_intensive_task, "IO", 1000),
            manager.run_in_document_pool(cpu_intensive_task, "Document", 1000),
            manager.run_in_api_pool(cpu_intensive_task, "API", 1000)
        )

        execution_time = time.time() - start_time

        # Vérifier que toutes les tâches se sont exécutées
        assert len(results) == 3
        assert all(":" in result for result in results)

        # Vérifier que l'exécution concurrente est efficace
        # This assertion is highly dependent on machine performance and GIL behavior for such tasks.
        # A slightly higher bound might be safer for general CI.
        assert execution_time < 2.0