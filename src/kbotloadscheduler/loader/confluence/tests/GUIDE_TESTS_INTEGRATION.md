# Guide des Tests d'Intégration avec Espace de Test

Ce guide vous explique comment tester votre système RAG Confluence avec un espace de test complet contenant des pages, sous-pages et pièces jointes.

## 🎯 Objectif

Valider le fonctionnement complet du système avec :
- Un espace Confluence structuré
- Une hiérarchie de pages et sous-pages
- Différents types de pièces jointes
- Différents types de contenu (pages, articles de blog)

## 🏗️ Structure de l'Espace de Test

L'espace de test généré contient :

### 📊 Données Générées
- **Espace** : `TESTSPACE` (Test Space)
- **Pages** : 8 pages au total
  - 7 pages normales
  - 1 article de blog
- **Hiérarchie** : 3 niveaux de profondeur
- **Pièces jointes** : Différents types (PDF, DOCX, PNG, etc.)

### 📋 Structure Hiérarchique
```
📄 Accueil - Documentation Technique (homepage123)
├── 📑 Architecture du Système (arch001)
│   ├── 📄 Microservices (arch002)
│   └── 📄 Base de Données (arch003)
└── 📑 Documentation API (api001)
    ├── 📄 API d'Authentification (api002)
    └── 📄 API Utilisateur (api003)

📄 Nouvelles Fonctionnalités Q1 2023 (blog001) [Article de blog]
```

## 🚀 Comment Tester

### 1. Tests avec Données Simulées

#### Afficher le résumé des données de test
```bash
python confluence_rag/tests/run_integration_tests.py --summary
```

#### Exécuter un scénario de test spécifique
```bash
python confluence_rag/tests/run_integration_tests.py --scenario
```

#### Exécuter tous les tests d'intégration
```bash
python confluence_rag/tests/run_integration_tests.py --run-tests --verbose
```

#### Exécuter des tests spécifiques
```bash
python confluence_rag/tests/run_integration_tests.py --run-tests --pattern "hierarchy"
```

### 2. Tests avec un Vrai Espace Confluence

#### Prérequis
1. Configurez votre fichier `.env` avec vos identifiants Confluence :
```bash
cp .env.example .env
# Éditez .env avec vos vraies valeurs
```

2. Assurez-vous d'avoir un espace de test dans Confluence

#### Analyser un espace existant
```bash
python test_with_real_confluence.py --space YOUR_SPACE_KEY --analyze
```

#### Synchronisation de test
```bash
python test_with_real_confluence.py --space YOUR_SPACE_KEY --sync --max-results 10
```

#### Test complet avec mode verbeux
```bash
python test_with_real_confluence.py --space YOUR_SPACE_KEY --analyze --sync --verbose
```

## 📁 Fichiers et Répertoires Créés

### Structure des Tests
```
confluence_rag/tests/
├── fixtures/
│   ├── __init__.py
│   ├── test_data_factory.py          # Factory pour créer des données de test
│   ├── test_criteria.json            # Critères de recherche de test
│   ├── sample_files/                 # Fichiers d'exemple
│   │   ├── sample.pdf
│   │   ├── sample.docx
│   │   ├── sample.png
│   │   ├── sample.drawio
│   │   └── ...
│   └── generate_sample_files.py      # Script pour générer les fichiers
├── test_integration_complete.py      # Tests d'intégration complets
└── run_integration_tests.py          # Script pour exécuter les tests
```

### Données Générées lors des Tests
```
/tmp/confluence_rag_tests/
├── contents/                         # Contenu des pages au format JSON
│   ├── homepage123.json
│   ├── arch001.json
│   └── ...
├── attachments/                      # Pièces jointes téléchargées
│   ├── homepage123/
│   │   ├── specification.pdf
│   │   └── architecture_diagram.png
│   └── ...
└── metadata/                         # Métadonnées et index
    ├── sync_history.json
    └── content_index.json
```

## 🧪 Types de Tests Disponibles

### Tests Unitaires avec Données de Test
- `test_page_hierarchy_structure` : Valide la structure hiérarchique
- `test_attachments_processing` : Teste le traitement des pièces jointes
- `test_content_retriever_with_test_data` : Teste la récupération de contenu
- `test_search_criteria_validation` : Valide les critères de recherche

### Tests d'Intégration
- `test_complete_workflow_with_test_space` : Workflow complet de bout en bout
- `test_storage_configuration` : Configuration de stockage
- `test_test_data_factory_consistency` : Cohérence des données générées

## 🔧 Personnalisation

### Modifier les Données de Test

Éditez `fixtures/test_data_factory.py` pour :
- Ajouter plus de pages
- Modifier la hiérarchie
- Changer les types de pièces jointes
- Personnaliser le contenu

### Ajouter de Nouveaux Types de Fichiers

1. Modifiez `fixtures/generate_sample_files.py`
2. Ajoutez les nouveaux types dans `StorageConfig`
3. Mettez à jour les tests correspondants

### Configurer les Critères de Recherche

Éditez `fixtures/test_criteria.json` :
```json
{
  "spaces": ["YOUR_SPACE"],
  "types": ["page", "blogpost"],
  "max_results": 50,
  "include_attachments": true,
  "include_children": true
}
```

## 📊 Validation des Résultats

### Vérifications Automatiques
- ✅ Nombre de pages récupérées
- ✅ Structure hiérarchique respectée
- ✅ Pièces jointes téléchargées
- ✅ Contenu chunké correctement
- ✅ Métadonnées générées

### Vérifications Manuelles
1. **Fichiers de contenu** : Vérifiez que les JSON contiennent le bon contenu
2. **Pièces jointes** : Vérifiez que les fichiers sont téléchargés
3. **Logs** : Examinez les logs pour détecter les erreurs
4. **Performance** : Vérifiez les temps de traitement

## 🐛 Dépannage

### Problèmes Courants

#### Tests qui échouent
```bash
# Exécuter un test spécifique en mode debug
pytest test_integration_complete.py::TestCompleteIntegration::test_complete_workflow_with_test_space -v -s
```

#### Problèmes de connexion Confluence
```bash
# Vérifier la configuration (depuis la racine du projet)
python test_with_real_confluence.py --space YOUR_SPACE --analyze --verbose
```

#### Fichiers manquants
```bash
# Régénérer les fichiers d'exemple
cd fixtures
python generate_sample_files.py
```

### Variables d'Environnement de Debug
```bash
export CONFLUENCE_RAG_ENVIRONMENT=test
export LOG_LEVEL=DEBUG
export SECURE_LOGGING=false
```

## 📈 Métriques et Performance

### Métriques Collectées
- Temps de traitement total
- Nombre d'éléments traités
- Nombre de pièces jointes
- Taux d'erreur
- Utilisation mémoire

### Optimisation
- Ajustez `max_parallel_downloads` selon vos ressources
- Modifiez `chunk_size` selon vos besoins
- Configurez les timeouts appropriés

## 🎯 Prochaines Étapes

1. **Exécutez les tests de base** avec `--summary` et `--scenario`
2. **Testez avec vos données** en utilisant `../../test_with_real_confluence.py`
3. **Personnalisez** les données de test selon vos besoins
4. **Intégrez** dans votre pipeline CI/CD

## 💡 Conseils

- Commencez toujours par les tests simulés avant les tests réels
- Utilisez un espace de test dédié, pas votre espace de production
- Surveillez les quotas d'API Confluence
- Sauvegardez vos configurations de test
- Documentez vos cas de test spécifiques
