#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour l'orchestrateur Confluence synchrone.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pydantic import SecretStr

from ..orchestrator import SyncOrchestrator
from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from ..models import ContentItem, AttachmentDetail, UserInfo, SpaceInfo
from ..exceptions import ConfluenceRAGException, AuthenticationError


class TestSyncOrchestrator(unittest.TestCase):
    """Tests pour SyncOrchestrator."""

    def setUp(self):
        """Configuration des tests."""
        # Configuration Confluence
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_pat_token"),
            default_space_key="TEST"
        )

        # Critères de recherche
        self.criteria = SearchCriteria(
            spaces=["TEST"],
            max_results=10,
            include_attachments=True
        )

        # Configuration de stockage
        self.storage_config = StorageConfig(
            type="filesystem",
            base_dir="./test_output"
        )

        # Configuration de traitement
        self.processing_config = ProcessingConfig(
            chunk_size=1000,
            overlap_size=200,
            max_parallel_downloads=3,
            max_thread_workers=2
        )

        # Données de test
        self.test_user = UserInfo(
            id="user123",
            username="testuser",
            display_name="Test User"
        )

        self.test_space = SpaceInfo(
            id="space123",
            key="TEST",
            name="Test Space",
            type="global"
        )

        self.test_content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.test_space,
            version={"number": 1},
            created=datetime.now(),
            creator=self.test_user,
            last_updated=datetime.now(),
            last_updater=self.test_user,
            content_url="https://test.atlassian.net/wiki/rest/api/content/123",
            web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            body_storage="<p>Test content</p>",
            body_view="<p>Test content</p>",
            body_plain="Test content"
        )

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_orchestrator_initialization(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de l'initialisation de l'orchestrateur synchrone."""
        # Setup mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )

        # Vérifications
        self.assertEqual(orchestrator.config, self.config)
        self.assertEqual(orchestrator.criteria, self.criteria)
        self.assertEqual(orchestrator.storage_config, self.storage_config)
        self.assertEqual(orchestrator.processing_config, self.processing_config)

        # Vérifier les appels de création
        mock_client.assert_called_once_with(self.config)
        mock_retriever.assert_called_once()
        mock_tracker.assert_called_once()
        mock_storage.assert_called_once_with("filesystem", base_dir="./test_output")
        mock_thread_pool.assert_called_once()

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_orchestrator_initialization_gcs(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de l'initialisation avec stockage GCS."""
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()
        
        gcs_config = StorageConfig(
            type="gcs",
            bucket_name="test-bucket",
            base_prefix="test-prefix"
        )
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            gcs_config,
            self.processing_config
        )
        
        mock_storage.assert_called_once_with(
            "gcs",
            bucket_name="test-bucket",
            base_prefix="test-prefix"
        )

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_orchestrator_initialization_invalid_storage(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de l'initialisation avec type de stockage invalide."""
        mock_thread_pool.return_value = Mock()
        
        invalid_config = StorageConfig(type="invalid")
        
        with self.assertRaises(ValueError) as context:
            SyncOrchestrator(
                self.config,
                self.criteria,
                invalid_config,
                self.processing_config
            )
        
        self.assertIn("Type de stockage non supporté", str(context.exception))

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_retrieve_content_success(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de récupération de contenu réussie."""
        # Setup mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()
        
        mock_content_items = [
            self.test_content_item,
            ContentItem(
                id="456",
                type="page",
                status="current",
                title="Another Page",
                space=self.test_space,
                version={"number": 1},
                created=datetime.now(),
                creator=self.test_user,
                last_updated=datetime.now(),
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/wiki/rest/api/content/456",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/456",
                body_storage="<p>Another content</p>",
                body_view="<p>Another content</p>",
                body_plain="Another content"
            )
        ]
        
        mock_retriever_instance = Mock()
        mock_retriever_instance.search_and_retrieve.return_value = mock_content_items
        mock_retriever.return_value = mock_retriever_instance
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        result = orchestrator._retrieve_content()
        
        self.assertEqual(result, mock_content_items)
        mock_retriever_instance.search_and_retrieve.assert_called_once_with(
            self.criteria,
            process_attachments=self.criteria.include_attachments
        )

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_retrieve_content_error(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de gestion d'erreur lors de la récupération."""
        # Setup mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()
        
        mock_retriever_instance = Mock()
        mock_retriever_instance.search_and_retrieve.side_effect = Exception("API Error")
        mock_retriever.return_value = mock_retriever_instance
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        with self.assertRaises(Exception):
            orchestrator._retrieve_content()
        
        # Vérifier que les stats d'erreur sont mises à jour
        self.assertEqual(orchestrator.stats["errors"], 1)

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider')
    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager')
    def test_process_changed_content(self, mock_thread_pool, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de traitement des contenus modifiés."""
        # Setup mocks
        mock_storage_instance = Mock()
        mock_storage.return_value = mock_storage_instance
        mock_thread_pool.return_value = Mock()
        
        mock_tracker_instance = Mock()
        mock_tracker_instance.has_content_changed.side_effect = [True, False]  # Premier changé, deuxième non
        mock_tracker.return_value = mock_tracker_instance
        
        content_items = [
            self.test_content_item,
            ContentItem(
                id="456",
                type="page",
                status="current",
                title="Unchanged Page",
                space=self.test_space,
                version={"number": 1},
                created=datetime.now(),
                creator=self.test_user,
                last_updated=datetime.now(),
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/wiki/rest/api/content/456",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/456",
                body_storage="<p>Unchanged content</p>",
                body_view="<p>Unchanged content</p>",
                body_plain="Unchanged content"
            )
        ]
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        # Mock de la méthode _store_content
        orchestrator._store_content = Mock(return_value="stored_path")
        
        result = orchestrator._process_changed_content(content_items)
        
        # Seul le premier élément devrait être dans le résultat (changé)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].id, "123")
        
        # Vérifier que _store_content a été appelé une seule fois
        orchestrator._store_content.assert_called_once_with(self.test_content_item)

    def test_get_sync_status(self):
        """Test de récupération du statut de synchronisation."""
        with patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient'), \
             patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever'), \
             patch('src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker') as mock_tracker, \
             patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider'), \
             patch('src.kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager'):
            
            mock_tracker_instance = Mock()
            mock_tracker_instance.get_last_sync_info.return_value = {"last_sync": "2023-01-01T12:00:00"}
            mock_tracker.return_value = mock_tracker_instance
            
            orchestrator = SyncOrchestrator(
                self.config,
                self.criteria,
                self.storage_config,
                self.processing_config
            )
            
            status = orchestrator.get_sync_status()
            
            self.assertIn("is_running", status)
            self.assertIn("last_sync", status)
            self.assertIn("current_stats", status)
            self.assertFalse(status["is_running"])  # Pas en cours d'exécution


if __name__ == '__main__':
    unittest.main()
