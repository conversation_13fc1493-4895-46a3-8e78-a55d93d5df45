#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module de stockage.
"""

import unittest
import tempfile
import shutil
import os
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from ..storage import (
    FileSystemStorage, get_storage_provider, StorageProvider
)


class TestFileSystemStorage(unittest.TestCase):
    """Tests pour la classe FileSystemStorage."""

    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        self.storage = FileSystemStorage(base_dir=self.temp_dir)

    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_filesystem_storage_initialization(self):
        """Test d'initialisation du stockage filesystem."""
        self.assertEqual(self.storage.base_dir, self.temp_dir)

        # Vérifier que les répertoires sont créés
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "contents")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "attachments")))

    def test_save_content(self):
        """Test de sauvegarde de contenu."""
        content_data = {
            "id": "page123",
            "title": "Test Page",
            "body": "Test content"
        }

        # Utiliser asyncio.run pour exécuter la méthode async
        result_path = asyncio.run(self.storage.save_content("page123", content_data))

        # Vérifier que le fichier a été créé
        expected_path = os.path.join(self.temp_dir, "contents", "page123.json")
        self.assertEqual(result_path, expected_path)
        self.assertTrue(os.path.exists(expected_path))

        # Vérifier le contenu du fichier
        with open(expected_path, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)

        self.assertEqual(saved_data["id"], "page123")
        self.assertEqual(saved_data["title"], "Test Page")
        self.assertEqual(saved_data["body"], "Test content")

    def test_save_attachment(self):
        """Test de sauvegarde de pièce jointe."""
        attachment_data = b"Binary file content"

        result_path = asyncio.run(
            self.storage.save_attachment("page123", "att456", "test.pdf", attachment_data)
        )

        # Vérifier que le fichier a été créé
        expected_path = os.path.join(self.temp_dir, "attachments", "page123", "att456_test.pdf")
        self.assertEqual(result_path, expected_path)
        self.assertTrue(os.path.exists(expected_path))

        # Vérifier le contenu du fichier
        with open(expected_path, 'rb') as f:
            saved_data = f.read()

        self.assertEqual(saved_data, attachment_data)

    def test_get_content(self):
        """Test de récupération de contenu."""
        # Sauvegarder d'abord un contenu
        content_data = {
            "id": "page123",
            "title": "Test Page",
            "body": "Test content"
        }
        asyncio.run(self.storage.save_content("page123", content_data))

        # Récupérer le contenu
        retrieved_data = asyncio.run(self.storage.get_content("page123"))

        self.assertIsNotNone(retrieved_data)
        self.assertEqual(retrieved_data["id"], "page123")
        self.assertEqual(retrieved_data["title"], "Test Page")
        self.assertEqual(retrieved_data["body"], "Test content")

    def test_get_content_not_found(self):
        """Test de récupération de contenu inexistant."""
        retrieved_data = asyncio.run(self.storage.get_content("nonexistent"))
        self.assertIsNone(retrieved_data)

    def test_get_attachment(self):
        """Test de récupération de pièce jointe."""
        # Sauvegarder d'abord une pièce jointe
        attachment_data = b"Binary file content"
        asyncio.run(
            self.storage.save_attachment("page123", "att456", "test.pdf", attachment_data)
        )

        # Récupérer la pièce jointe
        retrieved_data = asyncio.run(self.storage.get_attachment("page123", "att456"))

        self.assertIsNotNone(retrieved_data)
        self.assertEqual(retrieved_data, attachment_data)

    def test_get_attachment_not_found(self):
        """Test de récupération de pièce jointe inexistante."""
        retrieved_data = asyncio.run(self.storage.get_attachment("page123", "nonexistent"))
        self.assertIsNone(retrieved_data)

    def test_content_exists(self):
        """Test de vérification d'existence de contenu."""
        # Contenu inexistant
        exists = asyncio.run(self.storage.content_exists("nonexistent"))
        self.assertFalse(exists)

        # Sauvegarder un contenu
        content_data = {"id": "page123", "title": "Test Page"}
        asyncio.run(self.storage.save_content("page123", content_data))

        # Vérifier l'existence
        exists = asyncio.run(self.storage.content_exists("page123"))
        self.assertTrue(exists)

    def test_attachment_exists(self):
        """Test de vérification d'existence de pièce jointe."""
        # Pièce jointe inexistante
        exists = asyncio.run(self.storage.attachment_exists("page123", "nonexistent"))
        self.assertFalse(exists)

        # Sauvegarder une pièce jointe
        attachment_data = b"Binary content"
        asyncio.run(
            self.storage.save_attachment("page123", "att456", "test.pdf", attachment_data)
        )

        # Vérifier l'existence
        exists = asyncio.run(self.storage.attachment_exists("page123", "att456"))
        self.assertTrue(exists)

    def test_list_contents(self):
        """Test de listage des contenus."""
        # Sauvegarder plusieurs contenus
        for i in range(3):
            content_data = {"id": f"page{i}", "title": f"Test Page {i}"}
            asyncio.run(self.storage.save_content(f"page{i}", content_data))

        # Lister les contenus
        content_ids = asyncio.run(self.storage.list_contents())

        self.assertEqual(len(content_ids), 3)
        self.assertIn("page0", content_ids)
        self.assertIn("page1", content_ids)
        self.assertIn("page2", content_ids)

    def test_list_attachments(self):
        """Test de listage des pièces jointes."""
        # Sauvegarder plusieurs pièces jointes pour le même contenu
        for i in range(2):
            attachment_data = f"Binary content {i}".encode()
            asyncio.run(
                self.storage.save_attachment("page123", f"att{i}", f"test{i}.pdf", attachment_data)
            )

        # Lister les pièces jointes
        attachment_ids = asyncio.run(self.storage.list_attachments("page123"))

        self.assertEqual(len(attachment_ids), 2)
        self.assertIn("att0", attachment_ids)
        self.assertIn("att1", attachment_ids)

    def test_delete_content(self):
        """Test de suppression de contenu."""
        # Sauvegarder un contenu
        content_data = {"id": "page123", "title": "Test Page"}
        asyncio.run(self.storage.save_content("page123", content_data))

        # Vérifier qu'il existe
        exists = asyncio.run(self.storage.content_exists("page123"))
        self.assertTrue(exists)

        # Supprimer le contenu
        success = asyncio.run(self.storage.delete_content("page123"))
        self.assertTrue(success)

        # Vérifier qu'il n'existe plus
        exists = asyncio.run(self.storage.content_exists("page123"))
        self.assertFalse(exists)

    def test_delete_attachment(self):
        """Test de suppression de pièce jointe."""
        # Sauvegarder une pièce jointe
        attachment_data = b"Binary content"
        asyncio.run(
            self.storage.save_attachment("page123", "att456", "test.pdf", attachment_data)
        )

        # Vérifier qu'elle existe
        exists = asyncio.run(self.storage.attachment_exists("page123", "att456"))
        self.assertTrue(exists)

        # Supprimer la pièce jointe
        success = asyncio.run(self.storage.delete_attachment("page123", "att456"))
        self.assertTrue(success)

        # Vérifier qu'elle n'existe plus
        exists = asyncio.run(self.storage.attachment_exists("page123", "att456"))
        self.assertFalse(exists)

    def test_get_storage_stats(self):
        """Test de récupération des statistiques de stockage."""
        # Sauvegarder quelques contenus et pièces jointes
        for i in range(2):
            content_data = {"id": f"page{i}", "title": f"Test Page {i}"}
            asyncio.run(self.storage.save_content(f"page{i}", content_data))

            attachment_data = f"Binary content {i}".encode()
            asyncio.run(
                self.storage.save_attachment(f"page{i}", f"att{i}", f"test{i}.pdf", attachment_data)
            )

        # Récupérer les statistiques
        stats = asyncio.run(self.storage.get_storage_stats())

        self.assertEqual(stats["total_contents"], 2)
        self.assertEqual(stats["total_attachments"], 2)
        self.assertGreater(stats["total_size_bytes"], 0)


class TestStorageProviderFactory(unittest.TestCase):
    """Tests pour la factory de fournisseurs de stockage."""

    def test_get_filesystem_storage_provider(self):
        """Test de création d'un fournisseur filesystem."""
        with tempfile.TemporaryDirectory() as temp_dir:
            custom_path = os.path.join(temp_dir, "custom_storage")
            provider = get_storage_provider("filesystem", base_dir=custom_path)

            self.assertIsInstance(provider, FileSystemStorage)
            self.assertEqual(provider.base_dir, custom_path)

    def test_get_filesystem_storage_provider_default(self):
        """Test de création d'un fournisseur filesystem avec valeurs par défaut."""
        provider = get_storage_provider("filesystem")

        self.assertIsInstance(provider, FileSystemStorage)
        self.assertEqual(provider.base_dir, "output_data_dir")

    @patch('confluence_rag.storage.GCS_AVAILABLE', True)
    @patch('confluence_rag.storage.GCSStorage')
    def test_get_gcs_storage_provider(self, mock_gcs_storage):
        """Test de création d'un fournisseur GCS."""
        mock_instance = Mock()
        mock_gcs_storage.return_value = mock_instance

        provider = get_storage_provider("gcs", bucket_name="test-bucket", base_prefix="test-prefix")

        mock_gcs_storage.assert_called_once_with(bucket_name="test-bucket", base_prefix="test-prefix")
        self.assertEqual(provider, mock_instance)

    def test_get_gcs_storage_provider_missing_bucket(self):
        """Test d'erreur avec fournisseur GCS sans bucket."""
        with self.assertRaises(ValueError) as context:
            get_storage_provider("gcs")

        self.assertIn("bucket_name", str(context.exception))

    def test_get_unsupported_storage_provider(self):
        """Test d'erreur avec type de stockage non supporté."""
        with self.assertRaises(ValueError) as context:
            get_storage_provider("unsupported")

        self.assertIn("Type de stockage non supporté", str(context.exception))


if __name__ == '__main__':
    unittest.main()
