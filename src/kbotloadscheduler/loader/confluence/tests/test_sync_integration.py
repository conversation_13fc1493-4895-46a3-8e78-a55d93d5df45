#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration pour l'orchestrateur Confluence synchrone.
"""

import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pydantic import SecretStr

from ..orchestrator import SyncOrchestrator
from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig, ThreadPoolConfig
from ..models import ContentItem, AttachmentDetail, UserInfo, SpaceInfo
from .fixtures.test_data_factory import DataFactory, create_mock_confluence_client


class TestSyncIntegration(unittest.TestCase):
    """Tests d'intégration pour l'orchestrateur synchrone."""

    def setUp(self):
        """Configuration des tests."""
        # Répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()
        
        # Configuration Confluence
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_pat_token"),
            default_space_key="TEST"
        )

        # Critères de recherche
        self.criteria = SearchCriteria(
            spaces=["TEST"],
            max_results=5,
            include_attachments=True
        )

        # Configuration de stockage
        self.storage_config = StorageConfig(
            type="filesystem",
            base_dir=self.temp_dir
        )

        # Configuration de traitement
        self.processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=2,
            max_thread_workers=2,
            thread_pool_config=ThreadPoolConfig(
                io_thread_workers=3,
                document_processing_workers=2,
                api_thread_workers=2
            )
        )

        # Créer des données de test
        self.data_factory = DataFactory("TEST", "Test Space")
        self.test_pages = self.data_factory.create_test_page_hierarchy()
        self.test_attachments = self.data_factory.create_test_attachments()

    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_full_orchestration_workflow(self, mock_client_class):
        """Test du workflow complet d'orchestration."""
        # Créer un client mocké avec des données de test
        mock_client = create_mock_confluence_client(self.test_pages, self.test_attachments)
        mock_client_class.return_value = mock_client

        # Créer l'orchestrateur
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )

        # Mock du change tracker pour simuler des changements
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test_sync"})

        # Exécuter l'orchestration
        result = orchestrator.run()

        # Vérifications
        self.assertIsNotNone(result)
        self.assertIn("total_content_items", result)
        self.assertIn("changed_content_items", result)
        self.assertIn("stored_content_items", result)
        self.assertIn("execution_time", result)

        # Vérifier que des contenus ont été traités
        self.assertGreater(result["total_content_items"], 0)
        self.assertGreater(result["changed_content_items"], 0)

        # Vérifier les appels du client
        mock_client.search_content.assert_called()
        mock_client.get_content.assert_called()

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_orchestration_with_attachments(self, mock_client_class):
        """Test d'orchestration avec traitement des pièces jointes."""
        # Créer un client mocké avec des données de test incluant des pièces jointes
        mock_client = create_mock_confluence_client(self.test_pages, self.test_attachments)
        mock_client_class.return_value = mock_client

        # Mock du téléchargement de pièces jointes
        mock_client.download_attachment.return_value = b"Mock attachment content"

        # Configuration avec pièces jointes
        criteria_with_attachments = SearchCriteria(
            spaces=["TEST"],
            max_results=3,
            include_attachments=True
        )

        orchestrator = SyncOrchestrator(
            self.config,
            criteria_with_attachments,
            self.storage_config,
            self.processing_config
        )

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test_sync"})

        # Mock de l'extraction de texte
        with patch('src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor') as mock_extractor_class:
            mock_extractor = Mock()
            mock_extractor.extract_text.return_value = {"text": "Extracted text", "metadata": {}}
            mock_extractor_class.return_value = mock_extractor

            # Exécuter l'orchestration
            result = orchestrator.run()

            # Vérifications
            self.assertIsNotNone(result)
            self.assertGreater(result["total_content_items"], 0)

            # Vérifier que les pièces jointes ont été traitées
            if result["total_attachments"] > 0:
                mock_client.get_attachments.assert_called()
                mock_client.download_attachment.assert_called()

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_orchestration_error_handling(self, mock_client_class):
        """Test de gestion d'erreurs lors de l'orchestration."""
        # Créer un client mocké qui lève des erreurs
        mock_client = Mock()
        mock_client.search_content.side_effect = Exception("API Error")
        mock_client_class.return_value = mock_client

        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )

        # Exécuter l'orchestration (devrait gérer l'erreur)
        with self.assertRaises(Exception):
            orchestrator.run()

        # Vérifier que les stats d'erreur sont mises à jour
        self.assertGreater(orchestrator.stats["errors"], 0)

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_orchestration_no_changes(self, mock_client_class):
        """Test d'orchestration sans changements détectés."""
        # Créer un client mocké avec des données de test
        mock_client = create_mock_confluence_client(self.test_pages, self.test_attachments)
        mock_client_class.return_value = mock_client

        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )

        # Mock du change tracker pour simuler aucun changement
        orchestrator.change_tracker.has_content_changed = Mock(return_value=False)
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test_sync"})

        # Exécuter l'orchestration
        result = orchestrator.run()

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)
        self.assertEqual(result["changed_content_items"], 0)  # Aucun changement
        self.assertEqual(result["stored_content_items"], 0)   # Rien stocké

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_orchestration_performance_config(self, mock_client_class):
        """Test d'orchestration avec configuration de performance."""
        # Créer un client mocké
        mock_client = create_mock_confluence_client(self.test_pages, self.test_attachments)
        mock_client_class.return_value = mock_client

        # Configuration haute performance
        hp_processing_config = ProcessingConfig(
            chunk_size=1000,
            overlap_size=200,
            max_parallel_downloads=5,
            max_thread_workers=4,
            thread_pool_config=ThreadPoolConfig(
                io_thread_workers=8,
                document_processing_workers=4,
                api_thread_workers=6
            )
        )

        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            hp_processing_config
        )

        # Vérifier que la configuration est correctement appliquée
        self.assertEqual(orchestrator.processing_config.max_thread_workers, 4)
        self.assertEqual(orchestrator.processing_config.max_parallel_downloads, 5)
        self.assertEqual(orchestrator.processing_config.thread_pool_config.io_thread_workers, 8)

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test_sync"})

        # Exécuter l'orchestration
        result = orchestrator.run()

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)

    def test_orchestration_thread_pool_stats(self):
        """Test des statistiques des pools de threads."""
        with patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient'):
            orchestrator = SyncOrchestrator(
                self.config,
                self.criteria,
                self.storage_config,
                self.processing_config
            )

            # Récupérer les stats des pools de threads
            thread_stats = orchestrator.thread_pool_manager.get_pool_stats()

            # Vérifications
            self.assertIn("io", thread_stats)
            self.assertIn("document", thread_stats)
            self.assertIn("api", thread_stats)

            for pool_name, stats in thread_stats.items():
                self.assertIn("max_workers", stats)
                self.assertIn("active_threads", stats)
                self.assertIn("queue_size", stats)

    def test_sync_status_tracking(self):
        """Test du suivi du statut de synchronisation."""
        with patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient'):
            orchestrator = SyncOrchestrator(
                self.config,
                self.criteria,
                self.storage_config,
                self.processing_config
            )

            # Statut initial
            initial_status = orchestrator.get_sync_status()
            self.assertFalse(initial_status["is_running"])
            self.assertIsNone(initial_status["last_sync"])

            # Simuler une synchronisation en cours
            orchestrator.stats["start_time"] = datetime.now()
            running_status = orchestrator.get_sync_status()
            self.assertTrue(running_status["is_running"])

            # Simuler la fin de synchronisation
            orchestrator.stats["end_time"] = datetime.now()
            completed_status = orchestrator.get_sync_status()
            self.assertFalse(completed_status["is_running"])


if __name__ == '__main__':
    unittest.main()
