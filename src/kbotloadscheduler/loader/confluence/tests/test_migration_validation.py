#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de validation de la migration vers l'orchestrateur synchrone.
"""

import unittest
import tempfile
import shutil
import warnings
from unittest.mock import Mock, patch
from datetime import datetime
from pydantic import SecretStr

from ..orchestrator import SyncOrchestrator
from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from ..models import ContentItem, UserInfo, SpaceInfo
from .fixtures.test_data_factory import DataFactory, create_mock_confluence_client


class TestMigrationValidation(unittest.TestCase):
    """Tests de validation de la migration vers l'orchestrateur synchrone."""

    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Configuration de base
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token")
        )
        
        self.criteria = SearchCriteria(
            spaces=["TEST"],
            max_results=5
        )
        
        self.storage_config = StorageConfig(
            type="filesystem",
            base_dir=self.temp_dir
        )
        
        self.processing_config = ProcessingConfig(
            max_thread_workers=2,
            max_parallel_downloads=2
        )

    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_deprecated_imports_removed(self):
        """Test que les imports deprecated ont été supprimés."""
        # Ces imports ne devraient plus être disponibles
        with self.assertRaises(ImportError):
            from ..client import ConfluenceClient

        with self.assertRaises(ImportError):
            from ..processing.content_retriever import ContentRetriever

        with self.assertRaises(ImportError):
            from ..processing.attachment_processor import AttachmentProcessor

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_sync_orchestrator_replaces_async(self, mock_client_class):
        """Test que SyncOrchestrator remplace l'orchestrateur asyncio."""
        # Créer un client mocké
        mock_client = Mock()
        mock_client.search_content.return_value = []
        mock_client_class.return_value = mock_client
        
        # Créer l'orchestrateur synchrone
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        # Vérifier que c'est bien synchrone (pas de méthodes async)
        self.assertFalse(hasattr(orchestrator.run, '__await__'))
        
        # Mock du change tracker
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test"})
        
        # Exécuter de manière synchrone
        result = orchestrator.run()
        
        # Vérifications
        self.assertIsNotNone(result)
        self.assertIn("total_content_items", result)

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_no_asyncio_dependencies(self, mock_client_class):
        """Test qu'aucune dépendance asyncio n'est requise."""
        import sys
        
        # Créer un client mocké
        mock_client = Mock()
        mock_client.search_content.return_value = []
        mock_client_class.return_value = mock_client
        
        # Sauvegarder l'état d'asyncio
        asyncio_modules = [name for name in sys.modules.keys() if 'asyncio' in name]
        
        # Créer et utiliser l'orchestrateur
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        # Mock du change tracker
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test"})
        
        # Exécuter
        result = orchestrator.run()
        
        # Vérifier que le résultat est valide
        self.assertIsNotNone(result)
        
        # Note: On ne peut pas vraiment tester l'absence d'asyncio car
        # d'autres parties du système peuvent l'importer

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_performance_equivalent_or_better(self, mock_client_class):
        """Test que les performances sont équivalentes ou meilleures."""
        import time
        
        # Créer des données de test
        data_factory = DataFactory()
        test_pages = data_factory.create_test_page_hierarchy()
        
        mock_client = create_mock_confluence_client(test_pages, {})
        mock_client_class.return_value = mock_client
        
        # Mesurer le temps d'exécution
        start_time = time.time()
        
        orchestrator = SyncOrchestrator(
            self.config,
            self.criteria,
            self.storage_config,
            self.processing_config
        )
        
        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(return_value={"sync_id": "test"})
        
        result = orchestrator.run()
        
        execution_time = time.time() - start_time
        
        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)
        self.assertLess(execution_time, 10)  # Devrait être rapide pour un petit dataset

    def test_configuration_compatibility(self):
        """Test que toutes les configurations existantes sont compatibles."""
        # Test avec différentes configurations
        configs = [
            # Configuration minimale
            ConfluenceConfig(
                url="https://test.atlassian.net",
                pat_token=SecretStr("token")
            ),
            # Configuration complète
            ConfluenceConfig(
                url="https://test.atlassian.net",
                pat_token=SecretStr("token"),
                default_space_key="TEST",
                timeout=60
            )
        ]
        
        criterias = [
            # Critères simples
            SearchCriteria(spaces=["TEST"]),
            # Critères complexes
            SearchCriteria(
                spaces=["TEST1", "TEST2"],
                max_results=100,
                include_attachments=True,
                content_types=["page", "blogpost"],
                labels=["important"],
                title_contains="test"
            )
        ]
        
        storage_configs = [
            # Filesystem
            StorageConfig(type="filesystem", base_dir="/tmp"),
            # GCS (configuration seulement)
            StorageConfig(
                type="gcs",
                bucket_name="test-bucket",
                base_prefix="test/"
            )
        ]
        
        # Tester toutes les combinaisons
        for config in configs:
            for criteria in criterias:
                for storage_config in storage_configs:
                    with self.subTest(config=config, criteria=criteria, storage=storage_config):
                        try:
                            if storage_config.type == "gcs":
                                # Skip GCS car il nécessite des credentials
                                continue
                                
                            orchestrator = SyncOrchestrator(
                                config,
                                criteria,
                                storage_config,
                                self.processing_config
                            )
                            
                            # Vérifier que l'orchestrateur est créé correctement
                            self.assertIsNotNone(orchestrator.client)
                            self.assertIsNotNone(orchestrator.content_retriever)
                            self.assertIsNotNone(orchestrator.thread_pool_manager)
                            
                        except Exception as e:
                            if "invalid_grant" in str(e):
                                # Ignorer les erreurs d'authentification GCS
                                continue
                            raise

    @patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient')
    def test_error_handling_consistency(self, mock_client_class):
        """Test que la gestion d'erreurs est cohérente."""
        from ..exceptions import ConfluenceRAGException, AuthenticationError
        
        # Test avec différents types d'erreurs
        error_scenarios = [
            (AuthenticationError("Auth failed"), AuthenticationError),
            (ConfluenceRAGException("General error"), ConfluenceRAGException),
            (Exception("Unknown error"), Exception)
        ]
        
        for error, expected_type in error_scenarios:
            with self.subTest(error=error):
                mock_client = Mock()
                mock_client.search_content.side_effect = error
                mock_client_class.return_value = mock_client
                
                orchestrator = SyncOrchestrator(
                    self.config,
                    self.criteria,
                    self.storage_config,
                    self.processing_config
                )
                
                # Vérifier que l'erreur est propagée correctement
                with self.assertRaises(expected_type):
                    orchestrator.run()

    def test_thread_pool_configuration(self):
        """Test que la configuration des pools de threads fonctionne."""
        from ..config import ThreadPoolConfig
        
        # Configuration personnalisée des pools
        thread_config = ThreadPoolConfig(
            io_thread_workers=6,
            document_processing_workers=3,
            api_thread_workers=4
        )
        
        processing_config = ProcessingConfig(
            max_thread_workers=4,
            thread_pool_config=thread_config
        )
        
        with patch('src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient'):
            orchestrator = SyncOrchestrator(
                self.config,
                self.criteria,
                self.storage_config,
                processing_config
            )
            
            # Vérifier que les pools sont configurés correctement
            stats = orchestrator.thread_pool_manager.get_pool_stats()
            
            self.assertIn("io", stats)
            self.assertIn("document", stats)
            self.assertIn("api", stats)
            
            # Vérifier les tailles des pools
            self.assertEqual(stats["io"]["max_workers"], 6)
            self.assertEqual(stats["document"]["max_workers"], 3)
            self.assertEqual(stats["api"]["max_workers"], 4)

    def test_migration_completeness(self):
        """Test que la migration est complète."""
        # Vérifier que tous les composants synchrones sont disponibles
        from .. import (
            SyncConfluenceClient,
            SyncContentRetriever,
            SyncAttachmentProcessor,
            SyncOrchestrator
        )
        
        # Vérifier que les classes existent et sont utilisables
        self.assertTrue(hasattr(SyncConfluenceClient, '__init__'))
        self.assertTrue(hasattr(SyncContentRetriever, '__init__'))
        self.assertTrue(hasattr(SyncAttachmentProcessor, '__init__'))
        self.assertTrue(hasattr(SyncOrchestrator, '__init__'))
        
        # Vérifier que SyncOrchestrator a les bonnes méthodes
        self.assertTrue(hasattr(SyncOrchestrator, 'run'))
        self.assertTrue(hasattr(SyncOrchestrator, 'get_sync_status'))
        
        # Vérifier que run() n'est pas une coroutine
        self.assertFalse(hasattr(SyncOrchestrator.run, '__await__'))


if __name__ == '__main__':
    unittest.main()
