#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module de pagination.
"""

import unittest
from typing import List, Tuple

from ..pagination import PaginationCalculator
from ..constants import APIConstants


class TestPaginationCalculator(unittest.TestCase):
    """Tests pour la classe PaginationCalculator."""

    def test_calculate_pages_basic(self):
        """Test de calcul de pages basique."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=100,
            page_size=25
        )
        
        expected = [(0, 25), (25, 25), (50, 25), (75, 25)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_with_max_results_limit(self):
        """Test avec limitation par max_results."""
        pages = PaginationCalculator.calculate_pages(
            total_results=1000,
            max_results=50,
            page_size=20
        )
        
        expected = [(0, 20), (20, 20), (40, 10)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_exact_division(self):
        """Test avec division exacte."""
        pages = PaginationCalculator.calculate_pages(
            total_results=60,
            max_results=60,
            page_size=20
        )
        
        expected = [(0, 20), (20, 20), (40, 20)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_single_page(self):
        """Test avec une seule page."""
        pages = PaginationCalculator.calculate_pages(
            total_results=15,
            max_results=50,
            page_size=25
        )
        
        expected = [(0, 15)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_zero_results(self):
        """Test avec zéro résultat."""
        pages = PaginationCalculator.calculate_pages(
            total_results=0,
            max_results=50,
            page_size=25
        )
        
        self.assertEqual(pages, [])

    def test_calculate_pages_zero_page_size(self):
        """Test avec page_size zéro (utilise la valeur par défaut)."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=100,
            page_size=0
        )
        
        # Devrait utiliser DEFAULT_PAGE_SIZE
        expected_page_size = APIConstants.DEFAULT_PAGE_SIZE
        expected = [(0, expected_page_size), (expected_page_size, expected_page_size)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_negative_page_size(self):
        """Test avec page_size négatif (utilise la valeur par défaut)."""
        pages = PaginationCalculator.calculate_pages(
            total_results=75,
            max_results=75,
            page_size=-10
        )
        
        # Devrait utiliser DEFAULT_PAGE_SIZE
        expected_page_size = APIConstants.DEFAULT_PAGE_SIZE
        expected = [(0, expected_page_size), (expected_page_size, 25)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_max_results_zero(self):
        """Test avec max_results zéro."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=0,
            page_size=25
        )
        
        # max_results=0 signifie pas de limite, donc utilise total_results
        expected = [(0, 25), (25, 25), (50, 25), (75, 25)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_max_results_negative(self):
        """Test avec max_results négatif."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=-1,
            page_size=25
        )
        
        # max_results négatif signifie pas de limite, donc utilise total_results
        expected = [(0, 25), (25, 25), (50, 25), (75, 25)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_large_page_size(self):
        """Test avec page_size plus grand que total_results."""
        pages = PaginationCalculator.calculate_pages(
            total_results=30,
            max_results=30,
            page_size=100
        )
        
        expected = [(0, 30)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_remainder(self):
        """Test avec reste dans la division."""
        pages = PaginationCalculator.calculate_pages(
            total_results=77,
            max_results=77,
            page_size=20
        )
        
        expected = [(0, 20), (20, 20), (40, 20), (60, 17)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_max_results_smaller_than_page_size(self):
        """Test avec max_results plus petit que page_size."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=15,
            page_size=25
        )
        
        expected = [(0, 15)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_edge_case_one_result(self):
        """Test avec un seul résultat."""
        pages = PaginationCalculator.calculate_pages(
            total_results=1,
            max_results=1,
            page_size=10
        )
        
        expected = [(0, 1)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_realistic_scenario_small(self):
        """Test avec un scénario réaliste petit."""
        pages = PaginationCalculator.calculate_pages(
            total_results=150,
            max_results=100,
            page_size=25
        )
        
        expected = [(0, 25), (25, 25), (50, 25), (75, 25)]
        self.assertEqual(pages, expected)

    def test_calculate_pages_realistic_scenario_large(self):
        """Test avec un scénario réaliste grand."""
        pages = PaginationCalculator.calculate_pages(
            total_results=1000,
            max_results=500,
            page_size=50
        )
        
        expected = [
            (0, 50), (50, 50), (100, 50), (150, 50), (200, 50),
            (250, 50), (300, 50), (350, 50), (400, 50), (450, 50)
        ]
        self.assertEqual(pages, expected)

    def test_calculate_pages_return_type(self):
        """Test que le type de retour est correct."""
        pages = PaginationCalculator.calculate_pages(
            total_results=50,
            max_results=50,
            page_size=20
        )
        
        self.assertIsInstance(pages, list)
        for page in pages:
            self.assertIsInstance(page, tuple)
            self.assertEqual(len(page), 2)
            self.assertIsInstance(page[0], int)  # start
            self.assertIsInstance(page[1], int)  # limit

    def test_calculate_pages_start_values_sequential(self):
        """Test que les valeurs de start sont séquentielles."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=100,
            page_size=30
        )
        
        expected_starts = [0, 30, 60, 90]
        actual_starts = [page[0] for page in pages]
        self.assertEqual(actual_starts, expected_starts)

    def test_calculate_pages_limits_sum_to_total(self):
        """Test que la somme des limites correspond au total attendu."""
        total_results = 87
        max_results = 87
        page_size = 20
        
        pages = PaginationCalculator.calculate_pages(
            total_results=total_results,
            max_results=max_results,
            page_size=page_size
        )
        
        total_limit = sum(page[1] for page in pages)
        self.assertEqual(total_limit, total_results)

    def test_calculate_pages_no_empty_pages(self):
        """Test qu'aucune page vide n'est générée."""
        pages = PaginationCalculator.calculate_pages(
            total_results=100,
            max_results=100,
            page_size=25
        )
        
        for page in pages:
            self.assertGreater(page[1], 0)  # limit doit être > 0

    def test_calculate_pages_performance_large_dataset(self):
        """Test de performance avec un grand dataset."""
        pages = PaginationCalculator.calculate_pages(
            total_results=10000,
            max_results=5000,
            page_size=100
        )
        
        self.assertEqual(len(pages), 50)  # 5000 / 100 = 50 pages
        self.assertEqual(pages[0], (0, 100))
        self.assertEqual(pages[-1], (4900, 100))


if __name__ == '__main__':
    unittest.main()
