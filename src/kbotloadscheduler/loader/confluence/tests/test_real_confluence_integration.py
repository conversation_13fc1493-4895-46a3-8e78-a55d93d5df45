#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration avec un vrai espace Confluence.
"""

import os
import sys
import asyncio
import pytest
import tempfile
import json
import logging
from pathlib import Path
from dotenv import load_dotenv
from pydantic import SecretStr

# Ajouter le répertoire racine du projet au path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..'))

from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from ..orchestrator import SyncOrchestrator
from ..sync_client import SyncConfluenceClient
from ..logging_utils import CorrelationContext


@pytest.fixture
def config():
    """Configuration pour les tests d'intégration."""
    # Essayer de charger depuis les variables d'environnement
    load_dotenv()

    url = os.getenv("CONFLUENCE_BASE_URL") or os.getenv("CONFLUENCE_URL")
    username = os.getenv("CONFLUENCE_USERNAME")
    api_token = os.getenv("CONFLUENCE_TOKEN") or os.getenv("CONFLUENCE_API_TOKEN")
    pat_token = os.getenv("CONFLUENCE_PAT_TOKEN")

    # Si aucune configuration réelle n'est disponible, utiliser une configuration mock
    if not url:
        pytest.skip("Configuration Confluence non disponible - définissez CONFLUENCE_BASE_URL")

    if pat_token:
        return ConfluenceConfig(url=url, pat_token=SecretStr(pat_token))
    elif username and api_token:
        return ConfluenceConfig(url=url, username=username, api_token=SecretStr(api_token))
    else:
        pytest.skip("Tokens Confluence non disponibles - définissez CONFLUENCE_PAT_TOKEN ou CONFLUENCE_USERNAME/CONFLUENCE_API_TOKEN")


def test_connection(config: ConfluenceConfig):
    """Teste la connexion à Confluence."""
    print("🔗 Test de connexion à Confluence...")

    try:
        client = SyncConfluenceClient(config)

        # Test simple : récupérer les espaces
        print(f"   📡 Connexion à : {config.url}")

        # Ici vous pourriez ajouter un appel simple pour tester la connexion
        # Par exemple : spaces = await client.get_spaces()

        print("✅ Connexion réussie !")
        return True

    except Exception as e:
        print(f"❌ Erreur de connexion : {e}")
        return False


def analyze_space(config: ConfluenceConfig, space_key: str):
    """Analyse un espace Confluence pour comprendre sa structure."""
    print(f"🔍 Analyse de l'espace : {space_key}")

    try:
        client = SyncConfluenceClient(config)

        # Critères pour récupérer un échantillon de contenu
        criteria = SearchCriteria(
            spaces=[space_key],
            max_results=10,  # Limiter pour l'analyse
            include_attachments=True
        )

        print("   📊 Recherche d'un échantillon de contenu...")
        content_items = client.search_content(criteria)

        print(f"   📄 Trouvé {len(content_items)} éléments de contenu")

        # Analyser les types de contenu
        content_types = {}
        total_attachments = 0

        for item in content_items:
            content_types[item.type] = content_types.get(item.type, 0) + 1
            if hasattr(item, 'attachments') and item.attachments:
                total_attachments += len(item.attachments)

        print("   📋 Types de contenu trouvés :")
        for content_type, count in content_types.items():
            print(f"      - {content_type}: {count}")

        print(f"   📎 Total des pièces jointes : {total_attachments}")

        # Afficher quelques exemples
        print("   📝 Exemples de contenu :")
        for i, item in enumerate(content_items[:3]):
            print(f"      {i+1}. {item.title} ({item.type})")
            if hasattr(item, 'attachments') and item.attachments:
                print(f"         📎 {len(item.attachments)} pièce(s) jointe(s)")

        return content_items

    except Exception as e:
        print(f"❌ Erreur lors de l'analyse : {e}")
        return []


def run_test_sync(config: ConfluenceConfig, space_key: str, max_results: int = 20):
    """Exécute une synchronisation de test."""
    print(f"🚀 Synchronisation de test pour l'espace : {space_key}")

    # Créer un répertoire temporaire pour les résultats
    temp_dir = tempfile.mkdtemp(prefix="confluence_test_")
    print(f"📁 Répertoire de sortie : {temp_dir}")

    try:
        # Configuration des critères de recherche
        criteria = SearchCriteria(
            spaces=[space_key],
            max_results=max_results,
            include_attachments=True,
            include_children=True
        )

        # Configuration de stockage
        storage_config = StorageConfig(
            storage_type="filesystem",
            output_dir=temp_dir,
            attachment_extensions_to_download_raw=['.pdf', '.docx', '.xlsx', '.png'],
            attachment_extensions_to_convert=['.txt', '.html', '.json']
        )

        # Configuration de traitement
        processing_config = ProcessingConfig(
            chunk_size=1000,
            overlap_size=100,
            max_parallel_downloads=3
        )

        # Créer et exécuter l'orchestrateur
        orchestrator = SyncOrchestrator(
            config,
            criteria,
            storage_config,
            processing_config
        )

        print("   ⏳ Démarrage de la synchronisation...")
        stats = orchestrator.run()

        # Afficher les résultats
        print("\n📊 Résultats de la synchronisation :")
        print(f"   📄 Éléments de contenu traités : {stats.get('total_content_items', 0)}")
        print(f"   📎 Pièces jointes traitées : {stats.get('total_attachments', 0)}")
        print(f"   🔄 Éléments modifiés : {stats.get('changed_content_items', 0)}")
        print(f"   ⏱️  Temps de traitement : {stats.get('processing_time_seconds', 0):.2f}s")

        # Vérifier les fichiers créés
        contents_dir = Path(temp_dir) / "contents"
        if contents_dir.exists():
            content_files = list(contents_dir.glob("*.json"))
            print(f"   💾 Fichiers de contenu créés : {len(content_files)}")

        attachments_dir = Path(temp_dir) / "attachments"
        if attachments_dir.exists():
            attachment_files = list(attachments_dir.rglob("*"))
            attachment_files = [f for f in attachment_files if f.is_file()]
            print(f"   📎 Fichiers de pièces jointes : {len(attachment_files)}")

        print(f"\n💡 Vous pouvez examiner les résultats dans : {temp_dir}")

        return stats, temp_dir

    except Exception as e:
        print(f"❌ Erreur lors de la synchronisation : {e}")
        import traceback
        traceback.print_exc()
        return None, temp_dir


# Note: Ce fichier peut aussi être exécuté comme script autonome
# en définissant les variables d'environnement appropriées
