# Fichiers d'Exemple pour les Tests

Ce répertoire contient des fichiers d'exemple utilisés pour tester le traitement des pièces jointes.

## Fichiers Disponibles

- `sample.pdf` - Document PDF d'exemple
- `sample.docx` - Document Word d'exemple  
- `sample.xlsx` - Feuille de calcul Excel d'exemple
- `sample.png` - Image PNG d'exemple
- `sample.drawio` - Diagramme Draw.io d'exemple
- `sample.txt` - Fichier texte simple

## Utilisation

Ces fichiers sont utilisés par les tests pour simuler le téléchargement et le traitement de pièces jointes réelles depuis Confluence.

## Génération

Pour régénérer ces fichiers ou en créer de nouveaux, utilisez le script `generate_sample_files.py`.
