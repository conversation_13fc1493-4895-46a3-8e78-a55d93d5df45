#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Factory pour créer des données de test réalistes pour Confluence.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional
from unittest.mock import Mock

from ...models import (
    SpaceInfo, UserInfo, ContentItem, AttachmentDetail
)


class DataFactory:
    """Factory pour créer des données de test structurées."""

    def __init__(self, space_key: str = "TESTSPACE", space_name: str = "Test Space"):
        self.space_key = space_key
        self.space_name = space_name
        self.base_date = datetime(2023, 1, 1, 12, 0, 0)
        self.users = self._create_test_users()
        self.space = self._create_test_space()

    def _create_test_users(self) -> List[UserInfo]:
        """Crée des utilisateurs de test."""
        return [
            UserInfo(
                id="user1",
                username="alice.martin",
                display_name="<PERSON>"
            ),
            UserInfo(
                id="user2",
                username="bob.dupont",
                display_name="<PERSON>"
            ),
            UserInfo(
                id="user3",
                username="claire.berna<PERSON>",
                display_name="<PERSON>"
            )
        ]

    def _create_test_space(self) -> SpaceInfo:
        """Crée un espace de test."""
        return SpaceInfo(
            id="space123",
            key=self.space_key,
            name=self.space_name,
            type="global",
            description="Espace de test pour le système RAG Confluence",
            homepage_id="homepage123"
        )

    def create_page_hierarchy(self) -> List[ContentItem]:
        """Crée une hiérarchie de pages avec sous-pages."""
        pages = []

        # Page d'accueil
        homepage = self._create_page(
            page_id="homepage123",
            title="Accueil - Documentation Technique",
            content="<h1>Bienvenue dans la documentation technique</h1><p>Cette page contient l'ensemble de notre documentation.</p>",
            parent_id=None
        )
        pages.append(homepage)

        # Pages principales
        architecture_page = self._create_page(
            page_id="arch001",
            title="Architecture du Système",
            content="<h1>Architecture</h1><p>Description de l'architecture globale du système.</p><h2>Composants principaux</h2><ul><li>API Gateway</li><li>Services métier</li><li>Base de données</li></ul>",
            parent_id="homepage123"
        )
        pages.append(architecture_page)

        api_page = self._create_page(
            page_id="api001",
            title="Documentation API",
            content="<h1>API REST</h1><p>Documentation complète de notre API REST.</p><h2>Endpoints</h2><p>Liste des endpoints disponibles...</p>",
            parent_id="homepage123"
        )
        pages.append(api_page)

        # Sous-pages d'architecture
        microservices_page = self._create_page(
            page_id="arch002",
            title="Microservices",
            content="<h1>Architecture Microservices</h1><p>Détails sur notre architecture microservices.</p><h2>Services</h2><ul><li>Service utilisateur</li><li>Service commande</li><li>Service paiement</li></ul>",
            parent_id="arch001"
        )
        pages.append(microservices_page)

        database_page = self._create_page(
            page_id="arch003",
            title="Base de Données",
            content="<h1>Schéma de Base de Données</h1><p>Structure et organisation de nos données.</p><h2>Tables principales</h2><p>Description des tables...</p>",
            parent_id="arch001"
        )
        pages.append(database_page)

        # Sous-pages API
        auth_api_page = self._create_page(
            page_id="api002",
            title="API d'Authentification",
            content="<h1>Authentification</h1><p>Endpoints pour l'authentification et l'autorisation.</p><h2>JWT Tokens</h2><p>Utilisation des tokens JWT...</p>",
            parent_id="api001"
        )
        pages.append(auth_api_page)

        user_api_page = self._create_page(
            page_id="api003",
            title="API Utilisateur",
            content="<h1>Gestion des Utilisateurs</h1><p>CRUD operations pour les utilisateurs.</p><h2>Endpoints</h2><ul><li>GET /users</li><li>POST /users</li><li>PUT /users/{id}</li></ul>",
            parent_id="api001"
        )
        pages.append(user_api_page)

        # Page de blog
        blog_page = self._create_blog_post(
            page_id="blog001",
            title="Nouvelles Fonctionnalités Q1 2023",
            content="<h1>Nouvelles Fonctionnalités</h1><p>Présentation des nouvelles fonctionnalités développées ce trimestre.</p>"
        )
        pages.append(blog_page)

        return pages

    def _create_page(self, page_id: str, title: str, content: str, parent_id: Optional[str] = None) -> ContentItem:
        """Crée une page de test."""
        creator = self.users[0]  # Alice par défaut
        created_date = self.base_date + timedelta(days=int(page_id[-3:]) if page_id[-3:].isdigit() else 0)
        updated_date = created_date + timedelta(days=5)

        return ContentItem(
            id=page_id,
            type="page",
            status="current",
            title=title,
            space=self.space,
            version={"number": 2},
            created=created_date,
            creator=creator,
            last_updated=updated_date,
            last_updater=creator,
            content_url=f"https://test.atlassian.net/wiki/rest/api/content/{page_id}",
            web_ui_url=f"https://test.atlassian.net/wiki/spaces/{self.space_key}/pages/{page_id}",
            body_storage=content,
            body_view=content,
            body_plain=self._html_to_plain(content),
            parent_id=parent_id
        )

    def _create_blog_post(self, page_id: str, title: str, content: str) -> ContentItem:
        """Crée un article de blog de test."""
        creator = self.users[1]  # Bob par défaut
        created_date = self.base_date + timedelta(days=30)

        return ContentItem(
            id=page_id,
            type="blogpost",
            status="current",
            title=title,
            space=self.space,
            version={"number": 1},
            created=created_date,
            creator=creator,
            last_updated=created_date,
            last_updater=creator,
            content_url=f"https://test.atlassian.net/wiki/rest/api/content/{page_id}",
            web_ui_url=f"https://test.atlassian.net/wiki/spaces/{self.space_key}/pages/{page_id}",
            body_storage=content,
            body_view=content,
            body_plain=self._html_to_plain(content)
        )

    def _html_to_plain(self, html_content: str) -> str:
        """Convertit le HTML en texte brut (simulation simple)."""
        import re
        # Supprimer les balises HTML
        plain = re.sub(r'<[^>]+>', '', html_content)
        # Nettoyer les espaces multiples
        plain = re.sub(r'\s+', ' ', plain).strip()
        return plain

    def create_attachments_for_page(self, page_id: str, count: int = 3) -> List[AttachmentDetail]:
        """Crée des pièces jointes pour une page."""
        attachments = []
        creator = self.users[2]  # Claire par défaut

        attachment_types = [
            ("specification.pdf", "application/pdf", 2048000),
            ("architecture_diagram.png", "image/png", 512000),
            ("api_documentation.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 1024000),
            ("data_model.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 256000),
            ("workflow.drawio", "application/vnd.jgraph.mxfile", 128000)
        ]

        for i in range(min(count, len(attachment_types))):
            filename, media_type, file_size = attachment_types[i]
            att_id = f"att_{page_id}_{i+1}"

            attachment = AttachmentDetail(
                id=att_id,
                title=filename,
                file_name=filename,
                file_size=file_size,
                media_type=media_type,
                download_url=f"https://test.atlassian.net/wiki/download/attachments/{page_id}/{filename}",
                created=(self.base_date + timedelta(days=i+1)),
                last_updated=(self.base_date + timedelta(days=i+1)),
                creator=creator,
                content_id=page_id
            )
            attachments.append(attachment)

        return attachments


def create_test_space() -> SpaceInfo:
    """Fonction utilitaire pour créer un espace de test."""
    factory = DataFactory()
    return factory.space


def create_test_page_hierarchy() -> List[ContentItem]:
    """Fonction utilitaire pour créer une hiérarchie de pages de test."""
    factory = DataFactory()
    return factory.create_page_hierarchy()


def create_test_attachments(page_id: str = "homepage123", count: int = 3) -> List[AttachmentDetail]:
    """Fonction utilitaire pour créer des pièces jointes de test."""
    factory = DataFactory()
    return factory.create_attachments_for_page(page_id, count)


def create_mock_confluence_client(pages: List[ContentItem] = None, attachments: Dict[str, List[AttachmentDetail]] = None) -> Mock:
    """Crée un client Confluence mocké avec des données de test."""
    if pages is None:
        pages = create_test_page_hierarchy()

    if attachments is None:
        attachments = {}
        for page in pages:
            if page.id in ["homepage123", "arch001", "api001"]:  # Quelques pages avec des pièces jointes
                attachments[page.id] = create_test_attachments(page.id, 2)

    # Ajouter les pièces jointes aux pages
    for page in pages:
        page.attachments = attachments.get(page.id, [])

    # Créer un dictionnaire pour un accès rapide par ID
    pages_by_id = {page.id: page for page in pages}

    mock_client = Mock()
    mock_client.search_content = Mock(return_value=pages)
    mock_client.get_content = Mock(side_effect=lambda content_id: pages_by_id.get(content_id))
    mock_client.get_content_details = Mock(side_effect=lambda content_id: pages_by_id.get(content_id))
    mock_client.get_attachments = Mock(side_effect=lambda content_id: attachments.get(content_id, []))
    mock_client.download_attachment = Mock(return_value=b"Mock file content")

    return mock_client
