#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour les modèles de données.
"""

import unittest
from datetime import datetime
from pydantic import ValidationError

from ..models import (
    UserInfo, LabelInfo, SpaceInfo, AttachmentDetail, ContentItem
)


class TestUserInfo(unittest.TestCase):
    """Tests pour le modèle UserInfo."""

    def test_user_info_creation(self):
        """Test de création d'un UserInfo valide."""
        user = UserInfo(
            id="123",
            username="john.doe",
            display_name="<PERSON>",
            email="<EMAIL>",
            picture_url="https://example.com/avatar.jpg"
        )
        
        self.assertEqual(user.id, "123")
        self.assertEqual(user.username, "john.doe")
        self.assertEqual(user.display_name, "<PERSON>")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.picture_url, "https://example.com/avatar.jpg")

    def test_user_info_minimal(self):
        """Test de création d'un UserInfo avec champs minimaux."""
        user = UserInfo(
            id="123",
            username="john.doe",
            display_name="John Doe"
        )
        
        self.assertEqual(user.id, "123")
        self.assertEqual(user.username, "john.doe")
        self.assertEqual(user.display_name, "John Doe")
        self.assertIsNone(user.email)
        self.assertIsNone(user.picture_url)

    def test_user_info_validation_error(self):
        """Test d'erreur de validation pour UserInfo."""
        with self.assertRaises(ValidationError):
            UserInfo(username="john.doe")  # Manque id et display_name


class TestLabelInfo(unittest.TestCase):
    """Tests pour le modèle LabelInfo."""

    def test_label_info_creation(self):
        """Test de création d'un LabelInfo valide."""
        label = LabelInfo(
            id="label123",
            name="important",
            prefix="global"
        )
        
        self.assertEqual(label.id, "label123")
        self.assertEqual(label.name, "important")
        self.assertEqual(label.prefix, "global")

    def test_label_info_without_prefix(self):
        """Test de création d'un LabelInfo sans préfixe."""
        label = LabelInfo(
            id="label123",
            name="important"
        )
        
        self.assertEqual(label.id, "label123")
        self.assertEqual(label.name, "important")
        self.assertIsNone(label.prefix)


class TestSpaceInfo(unittest.TestCase):
    """Tests pour le modèle SpaceInfo."""

    def test_space_info_creation(self):
        """Test de création d'un SpaceInfo valide."""
        space = SpaceInfo(
            id="space123",
            key="TEST",
            name="Test Space",
            type="global",
            description="A test space",
            homepage_id="page123"
        )
        
        self.assertEqual(space.id, "space123")
        self.assertEqual(space.key, "TEST")
        self.assertEqual(space.name, "Test Space")
        self.assertEqual(space.type, "global")
        self.assertEqual(space.description, "A test space")
        self.assertEqual(space.homepage_id, "page123")

    def test_space_info_minimal(self):
        """Test de création d'un SpaceInfo avec champs minimaux."""
        space = SpaceInfo(
            id="space123",
            key="TEST",
            name="Test Space",
            type="global"
        )
        
        self.assertEqual(space.id, "space123")
        self.assertEqual(space.key, "TEST")
        self.assertEqual(space.name, "Test Space")
        self.assertEqual(space.type, "global")
        self.assertIsNone(space.description)
        self.assertIsNone(space.homepage_id)


class TestAttachmentDetail(unittest.TestCase):
    """Tests pour le modèle AttachmentDetail."""

    def setUp(self):
        """Configuration des tests."""
        self.creator = UserInfo(
            id="user123",
            username="john.doe",
            display_name="John Doe"
        )
        self.created_date = datetime(2023, 1, 1, 12, 0, 0)

    def test_attachment_detail_creation(self):
        """Test de création d'un AttachmentDetail valide."""
        attachment = AttachmentDetail(
            id="att123",
            title="Test Document",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=self.created_date,
            creator=self.creator,
            download_url="https://example.com/download/test.pdf",
            content_id="page123",
            content_type="page",
            extracted_text="Sample text content",
            processing_status="completed"
        )
        
        self.assertEqual(attachment.id, "att123")
        self.assertEqual(attachment.title, "Test Document")
        self.assertEqual(attachment.file_name, "test.pdf")
        self.assertEqual(attachment.file_size, 1024)
        self.assertEqual(attachment.media_type, "application/pdf")
        self.assertEqual(attachment.created, self.created_date)
        self.assertEqual(attachment.creator, self.creator)
        self.assertEqual(attachment.download_url, "https://example.com/download/test.pdf")
        self.assertEqual(attachment.content_id, "page123")
        self.assertEqual(attachment.content_type, "page")
        self.assertEqual(attachment.extracted_text, "Sample text content")
        self.assertEqual(attachment.processing_status, "completed")

    def test_attachment_detail_defaults(self):
        """Test des valeurs par défaut d'AttachmentDetail."""
        attachment = AttachmentDetail(
            id="att123",
            title="Test Document",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=self.created_date,
            creator=self.creator,
            download_url="https://example.com/download/test.pdf",
            content_id="page123"
        )
        
        self.assertIsNone(attachment.content_type)
        self.assertIsNone(attachment.extracted_text)
        self.assertEqual(attachment.processing_status, "pending")
        self.assertIsNone(attachment.processing_error)


class TestContentItem(unittest.TestCase):
    """Tests pour le modèle ContentItem."""

    def setUp(self):
        """Configuration des tests."""
        self.creator = UserInfo(
            id="user123",
            username="john.doe",
            display_name="John Doe"
        )
        self.space = SpaceInfo(
            id="space123",
            key="TEST",
            name="Test Space",
            type="global"
        )
        self.created_date = datetime(2023, 1, 1, 12, 0, 0)
        self.updated_date = datetime(2023, 1, 2, 12, 0, 0)

    def test_content_item_creation(self):
        """Test de création d'un ContentItem valide."""
        content = ContentItem(
            id="page123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space,
            version={"number": 1},
            created=self.created_date,
            creator=self.creator,
            last_updated=self.updated_date,
            last_updater=self.creator,
            content_url="https://example.com/api/content/page123",
            web_ui_url="https://example.com/pages/page123",
            body_storage="<p>Test content</p>",
            body_view="<p>Test content</p>",
            body_plain="Test content"
        )
        
        self.assertEqual(content.id, "page123")
        self.assertEqual(content.type, "page")
        self.assertEqual(content.status, "current")
        self.assertEqual(content.title, "Test Page")
        self.assertEqual(content.space, self.space)
        self.assertEqual(content.version, {"number": 1})
        self.assertEqual(content.created, self.created_date)
        self.assertEqual(content.creator, self.creator)
        self.assertEqual(content.last_updated, self.updated_date)
        self.assertEqual(content.last_updater, self.creator)

    def test_content_item_get_content_summary(self):
        """Test de la méthode get_content_summary."""
        # Test avec contenu court
        content = ContentItem(
            id="page123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space,
            version={"number": 1},
            created=self.created_date,
            creator=self.creator,
            last_updated=self.updated_date,
            last_updater=self.creator,
            content_url="https://example.com/api/content/page123",
            web_ui_url="https://example.com/pages/page123",
            body_plain="Short content"
        )
        
        summary = content.get_content_summary()
        self.assertEqual(summary, "Short content")
        
        # Test avec contenu long
        long_content = "A" * 300
        content.body_plain = long_content
        summary = content.get_content_summary(max_length=200)
        self.assertEqual(len(summary), 203)  # 200 + "..."
        self.assertTrue(summary.endswith("..."))
        
        # Test sans contenu
        content.body_plain = None
        summary = content.get_content_summary()
        self.assertEqual(summary, "Contenu non disponible")

    def test_content_item_defaults(self):
        """Test des valeurs par défaut de ContentItem."""
        content = ContentItem(
            id="page123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space,
            version={"number": 1},
            created=self.created_date,
            creator=self.creator,
            last_updated=self.updated_date,
            last_updater=self.creator,
            content_url="https://example.com/api/content/page123",
            web_ui_url="https://example.com/pages/page123"
        )
        
        self.assertIsNone(content.body_storage)
        self.assertIsNone(content.body_view)
        self.assertIsNone(content.body_plain)
        self.assertEqual(content.labels, [])
        self.assertEqual(content.attachments, [])
        self.assertIsNone(content.parent_id)
        self.assertEqual(content.ancestors, [])
        self.assertEqual(content.children, [])
        self.assertEqual(content.processed_chunks, [])


if __name__ == '__main__':
    unittest.main()
