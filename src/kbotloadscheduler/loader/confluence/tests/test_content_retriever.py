#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le récupérateur de contenu.
"""

import unittest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch

from ..processing.content_retriever import ContentRetriever
from ..models import ContentItem, SpaceInfo, UserInfo, AttachmentDetail
from ..config import SearchCriteria, ProcessingConfig
from ..exceptions import ContentProcessingError


class TestContentRetriever(unittest.TestCase):
    """Tests pour la classe ContentRetriever."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)

        # Mock du client Confluence
        self.mock_client = AsyncMock()

        # Configuration de traitement avec mock pour thread_pool_config
        self.processing_config = Mock()
        self.processing_config.chunk_size = 1000
        self.processing_config.overlap_size = 200
        self.processing_config.max_parallel_downloads = 5

        # Mock pour thread_pool_config avec valeurs numériques
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        self.processing_config.thread_pool_config = thread_pool_config

        # Créer des objets de test
        self.space_info = SpaceInfo(
            id="1",
            key="TEST",
            name="Test Space",
            type="global"
        )

        self.user_info = UserInfo(
            id="user1",
            username="testuser",
            display_name="Test User"
        )

        self.content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_view="<p>Test HTML content</p>",
            body_plain="Test plain content"
        )

    @patch('confluence_rag.processing.content_retriever.AttachmentProcessor')
    def test_content_retriever_initialization(self, mock_attachment_processor_class):
        """Test de l'initialisation du récupérateur de contenu."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        self.assertEqual(retriever.client, self.mock_client)
        self.assertEqual(retriever.config, self.processing_config)
        self.assertEqual(retriever.attachment_processor, mock_attachment_processor)
        self.assertIsNotNone(retriever.chunker)

        # Mock des statistiques du processeur de pièces jointes
        mock_attachment_processor.get_processing_stats.return_value = {}

        # Vérifier les statistiques initiales
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 0)
        self.assertEqual(stats["attachments_processed"], 0)
        self.assertEqual(stats["errors"], 0)

    @patch('confluence_rag.processing.content_retriever.AttachmentProcessor')
    @patch('confluence_rag.processing.content_retriever.ProcessingConfig.from_env')
    def test_content_retriever_default_config(self, mock_from_env, mock_attachment_processor_class):
        """Test de l'initialisation avec configuration par défaut."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor

        # Mock de la configuration par défaut
        mock_config = Mock()
        mock_config.chunk_size = 1500
        mock_config.overlap_size = 300
        mock_from_env.return_value = mock_config

        retriever = ContentRetriever(self.mock_client)

        self.assertEqual(retriever.config, mock_config)
        mock_from_env.assert_called_once()

    @patch('confluence_rag.processing.content_retriever.AttachmentProcessor')
    async def test_retrieve_content_success(self, mock_attachment_processor_class):
        """Test de récupération de contenu réussie."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour retourner le contenu
        self.mock_client.get_content.return_value = self.content_item

        # Mock du processeur de pièces jointes
        mock_attachment_processor.process_attachment = AsyncMock()
        mock_attachment_processor.get_processing_stats.return_value = {}

        # Mock du chunker
        mock_chunks = [
            {"chunk_id": "123_chunk_0", "content": "Test chunk 1"},
            {"chunk_id": "123_chunk_1", "content": "Test chunk 2"}
        ]
        retriever.chunker.create_chunks = Mock(return_value=mock_chunks)

        result = await retriever.retrieve_content("123", process_attachments=True)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.title, "Test Page")
        self.assertEqual(result.processed_chunks, mock_chunks)

        # Vérifier les appels
        self.mock_client.get_content.assert_called_once_with("123")
        retriever.chunker.create_chunks.assert_called_once_with(result)

        # Vérifier les statistiques
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 1)

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    async def test_retrieve_content_without_attachments(self, mock_get_thread_pool_manager):
        """Test de récupération de contenu sans traitement des pièces jointes."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client
        self.mock_client.get_content.return_value = self.content_item

        # Mock du chunker
        mock_chunks = [{"chunk_id": "123_chunk_0", "content": "Test chunk"}]
        retriever.chunker.create_chunks = Mock(return_value=mock_chunks)

        result = await retriever.retrieve_content("123", process_attachments=False)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.processed_chunks, mock_chunks)

        # Vérifier que le processeur de pièces jointes n'a pas été appelé
        retriever.attachment_processor.process_attachment = AsyncMock()
        retriever.attachment_processor.process_attachment.assert_not_called()

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    async def test_retrieve_content_error_handling(self, mock_get_thread_pool_manager):
        """Test de gestion d'erreur lors de la récupération."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour lever une exception
        self.mock_client.get_content.side_effect = Exception("API Error")

        with self.assertRaises(ContentProcessingError):
            await retriever.retrieve_content("123")

        # Vérifier les statistiques d'erreur
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 1)

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    async def test_search_and_retrieve_success(self, mock_get_thread_pool_manager):
        """Test de recherche et récupération réussie."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Créer des critères de recherche
        criteria = SearchCriteria(
            spaces=["TEST"],
            content_types=["page"],
            max_results=10
        )

        # Mock des résultats de recherche
        search_results = [
            Mock(id="123", title="Page 1"),
            Mock(id="456", title="Page 2")
        ]
        self.mock_client.search_content.return_value = search_results

        # Mock de retrieve_content
        retriever.retrieve_content = AsyncMock(side_effect=[
            self.content_item,
            ContentItem(
                id="456",
                type="page",
                status="current",
                title="Page 2",
                space=self.space_info,
                version={"number": 1},
                created="2023-01-01T12:00:00",
                creator=self.user_info,
                last_updated="2023-01-01T12:00:00",
                last_updater=self.user_info,
                content_url="https://example.com/content/456",
                web_ui_url="https://example.com/pages/456"
            )
        ])

        results = await retriever.search_and_retrieve(criteria, process_attachments=True)

        # Vérifications
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].id, "123")
        self.assertEqual(results[1].id, "456")

        # Vérifier les appels
        self.mock_client.search_content.assert_called_once_with(criteria)
        self.assertEqual(retriever.retrieve_content.call_count, 2)

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    async def test_search_and_retrieve_empty_results(self, mock_get_thread_pool_manager):
        """Test de recherche sans résultats."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Critères de recherche
        criteria = SearchCriteria(spaces=["EMPTY"], max_results=10)

        # Mock de recherche vide
        self.mock_client.search_content.return_value = []

        results = await retriever.search_and_retrieve(criteria)

        # Vérifications
        self.assertEqual(results, [])
        self.mock_client.search_content.assert_called_once_with(criteria)

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    async def test_search_and_retrieve_partial_failure(self, mock_get_thread_pool_manager):
        """Test de recherche avec échec partiel."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Critères de recherche
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        # Mock des résultats de recherche
        search_results = [
            Mock(id="123", title="Page 1"),
            Mock(id="456", title="Page 2"),
            Mock(id="789", title="Page 3")
        ]
        self.mock_client.search_content.return_value = search_results

        # Mock de retrieve_content avec un échec
        retriever.retrieve_content = AsyncMock(side_effect=[
            self.content_item,  # Succès
            Exception("Retrieval failed"),  # Échec
            self.content_item  # Succès
        ])

        results = await retriever.search_and_retrieve(criteria)

        # Vérifications - seulement 2 résultats sur 3
        self.assertEqual(len(results), 2)

        # Vérifier les statistiques d'erreur
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 1)

    @patch('confluence_rag.processing.content_retriever.AttachmentProcessor')
    def test_get_retrieval_stats(self, mock_attachment_processor_class):
        """Test de récupération des statistiques."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Modifier les statistiques internes
        retriever._stats["content_retrieved"] = 5
        retriever._stats["attachments_processed"] = 3
        retriever._stats["errors"] = 1

        # Mock des statistiques du processeur de pièces jointes
        mock_attachment_processor.get_processing_stats.return_value = {
            "processed": 10,
            "failed": 2,
            "skipped": 1
        }

        stats = retriever.get_retrieval_stats()

        # Vérifications
        self.assertEqual(stats["content_retrieved"], 5)
        self.assertEqual(stats["attachments_processed"], 3)
        self.assertEqual(stats["errors"], 1)
        self.assertEqual(stats["attachment_processed"], 10)
        self.assertEqual(stats["attachment_failed"], 2)
        self.assertEqual(stats["attachment_skipped"], 1)

    @patch('confluence_rag.processing.content_retriever.AttachmentProcessor')
    def test_reset_stats(self, mock_attachment_processor_class):
        """Test de remise à zéro des statistiques."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Modifier les statistiques
        retriever._stats["content_retrieved"] = 5
        retriever._stats["errors"] = 2

        # Mock du reset du processeur de pièces jointes
        mock_attachment_processor.reset_stats = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}

        # Remettre à zéro
        retriever.reset_stats()

        # Vérifications
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 0)
        self.assertEqual(stats["attachments_processed"], 0)
        self.assertEqual(stats["errors"], 0)

        # Vérifier que le reset du processeur a été appelé
        mock_attachment_processor.reset_stats.assert_called_once()

    @patch('confluence_rag.processing.content_retriever.get_thread_pool_manager')
    @patch('confluence_rag.processing.content_retriever.TextProcessor.html_to_plain_text')
    async def test_html_to_plain_text_conversion(self, mock_html_to_plain, mock_get_thread_pool_manager):
        """Test de conversion HTML vers texte brut."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock de la conversion HTML
        mock_html_to_plain.return_value = "Converted plain text"

        # Contenu avec HTML
        html_content = ContentItem(
            id="123",
            type="page",
            status="current",
            title="HTML Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_view="<p>HTML content</p>",
            body_plain=None
        )

        self.mock_client.get_content.return_value = html_content
        retriever.chunker.create_chunks = Mock(return_value=[])

        result = await retriever.retrieve_content("123", process_attachments=False)

        # Vérifications
        mock_html_to_plain.assert_called_once_with("<p>HTML content</p>")
        self.assertEqual(result.body_plain, "Converted plain text")


# Classe pour les tests asynchrones
class AsyncTestCase(unittest.TestCase):
    """Classe de base pour les tests asynchrones."""

    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        self.loop.close()

    def async_test(self, coro):
        """Exécute une coroutine dans la boucle d'événements."""
        return self.loop.run_until_complete(coro)


if __name__ == '__main__':
    unittest.main()
