#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test rapide pour valider la compatibilité GitLab CI/CD.
Script optimisé pour une exécution rapide en pipeline.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configuration du logging minimal
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def setup_ci_environment():
    """Configure l'environnement pour les tests CI."""
    os.environ['STORAGE_TYPE'] = 'filesystem'
    os.environ['TRACKING_STORAGE_TYPE'] = 'filesystem'
    os.environ['CONFLUENCE_RAG_ENVIRONMENT'] = 'test'
    os.environ['LOG_LEVEL'] = 'WARNING'
    os.environ['HEALTH_CHECK_ENABLED'] = 'false'
    os.environ['SECURE_LOGGING'] = 'true'


def test_basic_imports():
    """Test des imports de base."""
    logger.info("🔍 Test des imports de base")

    try:
        import confluence
        from ..tracking import ConfluenceChangeTracker
        from ..tracking_gcs import get_change_tracker
        logger.info("✅ Imports OK")
        return True
    except Exception as e:
        logger.error(f"❌ Import failed: {e}")
        return False


def test_factory():
    """Test de la factory de tracking."""
    logger.info("🔧 Test de la factory")

    try:
        from ..tracking_gcs import get_change_tracker
        tracker = get_change_tracker('filesystem', storage_dir='./test_ci')

        # Nettoyer
        import shutil
        if os.path.exists('./test_ci'):
            shutil.rmtree('./test_ci')

        logger.info("✅ Factory OK")
        return True
    except Exception as e:
        logger.error(f"❌ Factory failed: {e}")
        return False


def test_pytest_collection():
    """Test de la collecte des tests pytest."""
    logger.info("📋 Test de la collecte pytest")

    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "--collect-only", "-q"],
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            logger.info("✅ Collecte pytest OK")
            return True
        else:
            logger.error(f"❌ Collecte failed: {result.stderr[:100]}")
            return False
    except Exception as e:
        logger.error(f"❌ Collecte error: {e}")
        return False


def test_sample_unit_tests():
    """Test d'un échantillon de tests unitaires."""
    logger.info("🧪 Test d'échantillon unitaire")

    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "confluence_rag/tests/test_constants.py",
             "--disable-warnings", "-q"],
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            logger.info("✅ Tests unitaires OK")
            return True
        else:
            logger.error(f"❌ Tests failed: {result.stderr[:100]}")
            return False
    except Exception as e:
        logger.error(f"❌ Tests error: {e}")
        return False


def test_tracking_functionality():
    """Test de la fonctionnalité de tracking."""
    logger.info("🔄 Test du tracking")

    try:
        # Test plus spécifique et rapide
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "confluence_rag/tests/test_tracking.py::TestConfluenceChangeTracker::test_tracker_initialization",
             "--disable-warnings", "-q", "--tb=no"],
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            logger.info("✅ Tracking OK")
            return True
        else:
            logger.error(f"❌ Tracking failed: {result.stderr[:100]}")
            return False
    except Exception as e:
        logger.error(f"❌ Tracking error: {e}")
        return False


def main():
    """Fonction principale."""
    logger.info("🚀 Test rapide GitLab CI/CD")

    # Configuration de l'environnement
    setup_ci_environment()

    # Tests rapides
    tests = [
        ("Imports", test_basic_imports),
        ("Factory", test_factory),
        ("Pytest Collection", test_pytest_collection),
        ("Tests Unitaires", test_sample_unit_tests),
        ("Tracking", test_tracking_functionality),
    ]

    results = []
    for name, test_func in tests:
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            logger.error(f"❌ {name} - Exception: {e}")
            results.append((name, False))

    # Résumé
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)

    logger.info(f"\n📊 Résultat: {success_count}/{total_count}")

    for name, success in results:
        status = "✅" if success else "❌"
        logger.info(f"  {status} {name}")

    if success_count == total_count:
        logger.info("🎉 Tous les tests rapides sont OK!")
        sys.exit(0)
    else:
        logger.error("💥 Certains tests ont échoué!")
        sys.exit(1)


if __name__ == "__main__":
    main()
