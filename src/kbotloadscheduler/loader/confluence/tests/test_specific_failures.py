#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour tester spécifiquement les échecs restants.
"""

import subprocess
import sys

def run_test(test_path, description):
    """Exécute un test spécifique."""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test timeout après 60 secondes")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False

def main():
    """Fonction principale."""
    tests_to_check = [
        ("confluence_rag/tests/test_health_check.py", "Tests Health Check"),
        ("confluence_rag/tests/test_performance_optimization.py", "Tests Performance Optimization"),
        ("confluence_rag/tests/test_thread_pool_optimization.py", "Tests Thread Pool Optimization"),
        ("confluence_rag/tests/test_real_confluence_integration.py", "Tests Real Confluence Integration"),
    ]
    
    results = {}
    
    for test_path, description in tests_to_check:
        success = run_test(test_path, description)
        results[test_path] = success
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES TESTS")
    print(f"{'='*60}")
    
    for test_path, success in results.items():
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status}: {test_path}")
    
    total_success = sum(results.values())
    total_tests = len(results)
    print(f"\nRésultat global: {total_success}/{total_tests} tests réussis")
    
    return total_success == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
