#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour les exceptions personnalisées.
"""

import unittest
from unittest.mock import Mock

from ..exceptions import (
    ConfluenceRAGException, ConfigurationError, AuthenticationError,
    APIError, ContentNotFoundError, AttachmentProcessingError,
    ContentProcessingError, SecurityValidationError, RateLimitExceededError,
    CircuitOpenError
)


class TestConfluenceRAGException(unittest.TestCase):
    """Tests pour l'exception de base ConfluenceRAGException."""

    def test_confluence_rag_exception_creation(self):
        """Test de création de l'exception de base."""
        message = "Test exception message"
        exception = ConfluenceRAGException(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsInstance(exception, Exception)

    def test_confluence_rag_exception_inheritance(self):
        """Test que toutes les autres exceptions héritent de ConfluenceRAGException."""
        exceptions_to_test = [
            ConfigurationError,
            AuthenticationError,
            APIError,
            ContentNotFoundError,
            AttachmentProcessingError,
            ContentProcessingError,
            SecurityValidationError,
            RateLimitExceededError,
            CircuitOpenError
        ]
        
        for exception_class in exceptions_to_test:
            with self.subTest(exception_class=exception_class):
                exception = exception_class("Test message")
                self.assertIsInstance(exception, ConfluenceRAGException)


class TestConfigurationError(unittest.TestCase):
    """Tests pour ConfigurationError."""

    def test_configuration_error_creation(self):
        """Test de création d'une ConfigurationError."""
        message = "Invalid configuration parameter"
        exception = ConfigurationError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsInstance(exception, ConfluenceRAGException)


class TestAuthenticationError(unittest.TestCase):
    """Tests pour AuthenticationError."""

    def test_authentication_error_creation(self):
        """Test de création d'une AuthenticationError."""
        message = "Authentication failed"
        exception = AuthenticationError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsInstance(exception, ConfluenceRAGException)


class TestAPIError(unittest.TestCase):
    """Tests pour APIError."""

    def test_api_error_creation_basic(self):
        """Test de création d'une APIError basique."""
        message = "API request failed"
        exception = APIError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.status_code)
        self.assertIsNone(exception.response)
        self.assertIsInstance(exception, ConfluenceRAGException)

    def test_api_error_creation_with_status_code(self):
        """Test de création d'une APIError avec code de statut."""
        message = "API request failed"
        status_code = 404
        exception = APIError(message, status_code=status_code)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.status_code, status_code)
        self.assertIsNone(exception.response)

    def test_api_error_creation_with_response(self):
        """Test de création d'une APIError avec réponse."""
        message = "API request failed"
        status_code = 500
        response = Mock()
        response.text = "Internal Server Error"
        
        exception = APIError(message, status_code=status_code, response=response)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.status_code, status_code)
        self.assertEqual(exception.response, response)

    def test_api_error_creation_complete(self):
        """Test de création d'une APIError avec tous les paramètres."""
        message = "API request failed"
        status_code = 403
        response = {"error": "Forbidden"}
        
        exception = APIError(message, status_code=status_code, response=response)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.status_code, status_code)
        self.assertEqual(exception.response, response)


class TestContentNotFoundError(unittest.TestCase):
    """Tests pour ContentNotFoundError."""

    def test_content_not_found_error_creation(self):
        """Test de création d'une ContentNotFoundError."""
        message = "Content not found"
        exception = ContentNotFoundError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsInstance(exception, ConfluenceRAGException)


class TestAttachmentProcessingError(unittest.TestCase):
    """Tests pour AttachmentProcessingError."""

    def test_attachment_processing_error_basic(self):
        """Test de création d'une AttachmentProcessingError basique."""
        message = "Failed to process attachment"
        exception = AttachmentProcessingError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.attachment_id)
        self.assertIsNone(exception.file_name)
        self.assertIsInstance(exception, ConfluenceRAGException)

    def test_attachment_processing_error_with_attachment_id(self):
        """Test de création d'une AttachmentProcessingError avec ID de pièce jointe."""
        message = "Failed to process attachment"
        attachment_id = "att123456"
        exception = AttachmentProcessingError(message, attachment_id=attachment_id)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.attachment_id, attachment_id)
        self.assertIsNone(exception.file_name)

    def test_attachment_processing_error_with_file_name(self):
        """Test de création d'une AttachmentProcessingError avec nom de fichier."""
        message = "Failed to process attachment"
        file_name = "document.pdf"
        exception = AttachmentProcessingError(message, file_name=file_name)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.attachment_id)
        self.assertEqual(exception.file_name, file_name)

    def test_attachment_processing_error_complete(self):
        """Test de création d'une AttachmentProcessingError avec tous les paramètres."""
        message = "Failed to process attachment"
        attachment_id = "att123456"
        file_name = "document.pdf"
        exception = AttachmentProcessingError(
            message, 
            attachment_id=attachment_id, 
            file_name=file_name
        )
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.attachment_id, attachment_id)
        self.assertEqual(exception.file_name, file_name)


class TestContentProcessingError(unittest.TestCase):
    """Tests pour ContentProcessingError."""

    def test_content_processing_error_basic(self):
        """Test de création d'une ContentProcessingError basique."""
        message = "Failed to process content"
        exception = ContentProcessingError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.content_id)
        self.assertIsNone(exception.content_title)
        self.assertIsInstance(exception, ConfluenceRAGException)

    def test_content_processing_error_with_content_id(self):
        """Test de création d'une ContentProcessingError avec ID de contenu."""
        message = "Failed to process content"
        content_id = "page123456"
        exception = ContentProcessingError(message, content_id=content_id)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.content_id, content_id)
        self.assertIsNone(exception.content_title)

    def test_content_processing_error_with_content_title(self):
        """Test de création d'une ContentProcessingError avec titre de contenu."""
        message = "Failed to process content"
        content_title = "Test Page"
        exception = ContentProcessingError(message, content_title=content_title)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.content_id)
        self.assertEqual(exception.content_title, content_title)

    def test_content_processing_error_complete(self):
        """Test de création d'une ContentProcessingError avec tous les paramètres."""
        message = "Failed to process content"
        content_id = "page123456"
        content_title = "Test Page"
        exception = ContentProcessingError(
            message, 
            content_id=content_id, 
            content_title=content_title
        )
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.content_id, content_id)
        self.assertEqual(exception.content_title, content_title)


class TestSecurityValidationError(unittest.TestCase):
    """Tests pour SecurityValidationError."""

    def test_security_validation_error_creation(self):
        """Test de création d'une SecurityValidationError."""
        message = "Security validation failed"
        exception = SecurityValidationError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsInstance(exception, ConfluenceRAGException)


class TestRateLimitExceededError(unittest.TestCase):
    """Tests pour RateLimitExceededError."""

    def test_rate_limit_exceeded_error_basic(self):
        """Test de création d'une RateLimitExceededError basique."""
        message = "Rate limit exceeded"
        exception = RateLimitExceededError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.retry_after)
        self.assertIsInstance(exception, ConfluenceRAGException)

    def test_rate_limit_exceeded_error_with_retry_after(self):
        """Test de création d'une RateLimitExceededError avec retry_after."""
        message = "Rate limit exceeded"
        retry_after = 60.0
        exception = RateLimitExceededError(message, retry_after=retry_after)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.retry_after, retry_after)


class TestCircuitOpenError(unittest.TestCase):
    """Tests pour CircuitOpenError."""

    def test_circuit_open_error_basic(self):
        """Test de création d'une CircuitOpenError basique."""
        message = "Circuit breaker is open"
        exception = CircuitOpenError(message)
        
        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.service_name)
        self.assertIsNone(exception.reset_timeout)
        self.assertIsNone(exception.time_remaining)
        self.assertIsInstance(exception, ConfluenceRAGException)

    def test_circuit_open_error_with_service_name(self):
        """Test de création d'une CircuitOpenError avec nom de service."""
        message = "Circuit breaker is open"
        service_name = "confluence-api"
        exception = CircuitOpenError(message, service_name=service_name)
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.service_name, service_name)
        self.assertIsNone(exception.reset_timeout)
        self.assertIsNone(exception.time_remaining)

    def test_circuit_open_error_complete(self):
        """Test de création d'une CircuitOpenError avec tous les paramètres."""
        message = "Circuit breaker is open"
        service_name = "confluence-api"
        reset_timeout = 60
        time_remaining = 45.5
        
        exception = CircuitOpenError(
            message,
            service_name=service_name,
            reset_timeout=reset_timeout,
            time_remaining=time_remaining
        )
        
        self.assertEqual(str(exception), message)
        self.assertEqual(exception.service_name, service_name)
        self.assertEqual(exception.reset_timeout, reset_timeout)
        self.assertEqual(exception.time_remaining, time_remaining)


if __name__ == '__main__':
    unittest.main()
