#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le découpage de contenu.
"""

import unittest
from unittest.mock import Mock, patch

from ..processing.content_chunker import ContentChunker
from ..models import ContentItem, SpaceInfo, UserInfo


class TestContentChunker(unittest.TestCase):
    """Tests pour la classe ContentChunker."""

    def setUp(self):
        """Configuration des tests."""
        self.chunker = ContentChunker(chunk_size=100, overlap_size=20)
        
        # Créer des objets de test
        self.space_info = SpaceInfo(
            id="1",
            key="TEST",
            name="Test Space",
            type="global"
        )
        
        self.user_info = UserInfo(
            id="user1",
            username="testuser",
            display_name="Test User"
        )
        
        self.content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_plain="This is a test content that will be chunked into smaller pieces for testing purposes."
        )

    def test_chunker_initialization(self):
        """Test de l'initialisation du chunker."""
        self.assertEqual(self.chunker.chunk_size, 100)
        self.assertEqual(self.chunker.overlap_size, 20)

    def test_chunker_custom_parameters(self):
        """Test de l'initialisation avec paramètres personnalisés."""
        custom_chunker = ContentChunker(chunk_size=500, overlap_size=50)
        
        self.assertEqual(custom_chunker.chunk_size, 500)
        self.assertEqual(custom_chunker.overlap_size, 50)

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_create_chunks_basic(self, mock_chunk_text):
        """Test de création de chunks basique."""
        # Mock du TextProcessor
        mock_chunk_text.return_value = [
            "This is a test content that will be chunked",
            "chunked into smaller pieces for testing purposes."
        ]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        # Vérifier l'appel au TextProcessor
        mock_chunk_text.assert_called_once_with(
            self.content_item.body_plain,
            chunk_size=100,
            overlap=20
        )
        
        # Vérifier les chunks créés
        self.assertEqual(len(chunks), 2)
        
        # Vérifier le premier chunk
        first_chunk = chunks[0]
        self.assertEqual(first_chunk["chunk_id"], "123_chunk_0")
        self.assertEqual(first_chunk["content"], "This is a test content that will be chunked")
        self.assertEqual(first_chunk["start_index"], 0)
        self.assertIn("metadata", first_chunk)
        
        # Vérifier le deuxième chunk
        second_chunk = chunks[1]
        self.assertEqual(second_chunk["chunk_id"], "123_chunk_1")
        self.assertEqual(second_chunk["content"], "chunked into smaller pieces for testing purposes.")
        self.assertEqual(second_chunk["start_index"], 80)  # (100 - 20) = 80
        self.assertIn("metadata", second_chunk)

    def test_create_chunks_empty_content(self):
        """Test de création de chunks avec contenu vide."""
        # Contenu sans body_plain
        empty_content = ContentItem(
            id="456",
            type="page",
            status="current",
            title="Empty Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/456",
            web_ui_url="https://example.com/pages/456",
            body_plain=None
        )
        
        chunks = self.chunker.create_chunks(empty_content)
        
        self.assertEqual(chunks, [])

    def test_create_chunks_empty_string(self):
        """Test de création de chunks avec chaîne vide."""
        # Contenu avec body_plain vide
        empty_content = ContentItem(
            id="789",
            type="page",
            status="current",
            title="Empty String Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/789",
            web_ui_url="https://example.com/pages/789",
            body_plain=""
        )
        
        chunks = self.chunker.create_chunks(empty_content)
        
        self.assertEqual(chunks, [])

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_create_chunks_single_chunk(self, mock_chunk_text):
        """Test de création d'un seul chunk."""
        # Mock pour retourner un seul chunk
        mock_chunk_text.return_value = ["Short content"]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        self.assertEqual(len(chunks), 1)
        
        chunk = chunks[0]
        self.assertEqual(chunk["chunk_id"], "123_chunk_0")
        self.assertEqual(chunk["content"], "Short content")
        self.assertEqual(chunk["start_index"], 0)

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_create_chunks_multiple_chunks(self, mock_chunk_text):
        """Test de création de plusieurs chunks."""
        # Mock pour retourner plusieurs chunks
        mock_chunk_text.return_value = [
            "First chunk of content",
            "Second chunk of content",
            "Third chunk of content",
            "Fourth chunk of content"
        ]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        self.assertEqual(len(chunks), 4)
        
        # Vérifier les IDs des chunks
        expected_ids = ["123_chunk_0", "123_chunk_1", "123_chunk_2", "123_chunk_3"]
        actual_ids = [chunk["chunk_id"] for chunk in chunks]
        self.assertEqual(actual_ids, expected_ids)
        
        # Vérifier les indices de début
        expected_start_indices = [0, 80, 160, 240]  # (chunk_size - overlap_size) * index
        actual_start_indices = [chunk["start_index"] for chunk in chunks]
        self.assertEqual(actual_start_indices, expected_start_indices)

    def test_calculate_start_index(self):
        """Test de calcul d'index de début."""
        # Premier chunk
        self.assertEqual(self.chunker._calculate_start_index(0), 0)
        
        # Deuxième chunk
        self.assertEqual(self.chunker._calculate_start_index(1), 80)  # 100 - 20
        
        # Troisième chunk
        self.assertEqual(self.chunker._calculate_start_index(2), 160)  # 2 * (100 - 20)
        
        # Quatrième chunk
        self.assertEqual(self.chunker._calculate_start_index(3), 240)  # 3 * (100 - 20)

    def test_calculate_start_index_different_parameters(self):
        """Test de calcul d'index avec paramètres différents."""
        chunker = ContentChunker(chunk_size=200, overlap_size=50)
        
        # Premier chunk
        self.assertEqual(chunker._calculate_start_index(0), 0)
        
        # Deuxième chunk
        self.assertEqual(chunker._calculate_start_index(1), 150)  # 200 - 50
        
        # Troisième chunk
        self.assertEqual(chunker._calculate_start_index(2), 300)  # 2 * (200 - 50)

    def test_create_chunk_metadata(self):
        """Test de création de métadonnées de chunk."""
        metadata = self.chunker._create_chunk_metadata(self.content_item)
        
        expected_metadata = {
            "title": "Test Page",
            "space_key": "TEST",
            "content_type": "page",
            "url": "https://example.com/pages/123"
        }
        
        self.assertEqual(metadata, expected_metadata)

    def test_create_chunk_metadata_different_content(self):
        """Test de création de métadonnées pour différents types de contenu."""
        # Créer un contenu de type blog
        blog_content = ContentItem(
            id="blog123",
            type="blogpost",
            status="current",
            title="Test Blog Post",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/blog123",
            web_ui_url="https://example.com/blog/blog123",
            body_plain="Blog content"
        )
        
        metadata = self.chunker._create_chunk_metadata(blog_content)
        
        expected_metadata = {
            "title": "Test Blog Post",
            "space_key": "TEST",
            "content_type": "blogpost",
            "url": "https://example.com/blog/blog123"
        }
        
        self.assertEqual(metadata, expected_metadata)

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_chunk_metadata_consistency(self, mock_chunk_text):
        """Test de cohérence des métadonnées entre chunks."""
        # Mock pour retourner plusieurs chunks
        mock_chunk_text.return_value = ["Chunk 1", "Chunk 2", "Chunk 3"]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        # Vérifier que tous les chunks ont les mêmes métadonnées
        first_metadata = chunks[0]["metadata"]
        for chunk in chunks[1:]:
            self.assertEqual(chunk["metadata"], first_metadata)

    def test_chunker_with_zero_overlap(self):
        """Test du chunker avec chevauchement zéro."""
        chunker = ContentChunker(chunk_size=100, overlap_size=0)
        
        # Test de calcul d'index
        self.assertEqual(chunker._calculate_start_index(0), 0)
        self.assertEqual(chunker._calculate_start_index(1), 100)
        self.assertEqual(chunker._calculate_start_index(2), 200)

    def test_chunker_with_large_overlap(self):
        """Test du chunker avec grand chevauchement."""
        chunker = ContentChunker(chunk_size=100, overlap_size=90)
        
        # Test de calcul d'index
        self.assertEqual(chunker._calculate_start_index(0), 0)
        self.assertEqual(chunker._calculate_start_index(1), 10)  # 100 - 90
        self.assertEqual(chunker._calculate_start_index(2), 20)  # 2 * (100 - 90)

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_chunk_structure_validation(self, mock_chunk_text):
        """Test de validation de la structure des chunks."""
        mock_chunk_text.return_value = ["Test chunk content"]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        self.assertEqual(len(chunks), 1)
        chunk = chunks[0]
        
        # Vérifier que le chunk a tous les champs requis
        required_fields = ["chunk_id", "content", "start_index", "metadata"]
        for field in required_fields:
            self.assertIn(field, chunk)
        
        # Vérifier les types
        self.assertIsInstance(chunk["chunk_id"], str)
        self.assertIsInstance(chunk["content"], str)
        self.assertIsInstance(chunk["start_index"], int)
        self.assertIsInstance(chunk["metadata"], dict)
        
        # Vérifier la structure des métadonnées
        metadata_fields = ["title", "space_key", "content_type", "url"]
        for field in metadata_fields:
            self.assertIn(field, chunk["metadata"])

    @patch('confluence_rag.processing.content_chunker.TextProcessor.chunk_text')
    def test_chunk_id_uniqueness(self, mock_chunk_text):
        """Test de l'unicité des IDs de chunks."""
        mock_chunk_text.return_value = ["Chunk 1", "Chunk 2", "Chunk 3", "Chunk 4", "Chunk 5"]
        
        chunks = self.chunker.create_chunks(self.content_item)
        
        # Extraire tous les IDs
        chunk_ids = [chunk["chunk_id"] for chunk in chunks]
        
        # Vérifier l'unicité
        self.assertEqual(len(chunk_ids), len(set(chunk_ids)))
        
        # Vérifier le format des IDs
        for i, chunk_id in enumerate(chunk_ids):
            expected_id = f"123_chunk_{i}"
            self.assertEqual(chunk_id, expected_id)

    def test_chunker_edge_cases(self):
        """Test des cas limites du chunker."""
        # Test avec chunk_size = 1
        tiny_chunker = ContentChunker(chunk_size=1, overlap_size=0)
        self.assertEqual(tiny_chunker.chunk_size, 1)
        self.assertEqual(tiny_chunker.overlap_size, 0)
        
        # Test avec overlap_size égal à chunk_size
        equal_chunker = ContentChunker(chunk_size=50, overlap_size=50)
        self.assertEqual(equal_chunker._calculate_start_index(1), 0)  # 50 - 50 = 0


if __name__ == '__main__':
    unittest.main()
