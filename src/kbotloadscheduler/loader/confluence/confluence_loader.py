import logging
import os
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from kbotloadscheduler.bean.beans import DocumentBean, Metadata, SourceBean
from kbotloadscheduler.gcs.gcs_utils import create_file_with_content
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

from ..abstract_loader import AbstractLoader, LoaderException

# Import du module confluence existant
from .config import (
    ConfluenceConfig,
    ProcessingConfig,
    SearchCriteria,
    StorageConfig,
)
from .exceptions import ConfluenceRAGException
from .models import ContentItem
from .orchestrator import SyncOrchestrator


class ConfluenceLoader(AbstractLoader):
    """Loader pour Confluence intégré dans l'architecture kbot-load-scheduler"""

    def __init__(self, config_with_secret: ConfigWithSecret):
        super().__init__("confluence")
        self.config_with_secret = config_with_secret
        self.logger = logging.getLogger(__name__)

        # Configuration par défaut depuis les variables d'environnement
        self.confluence_url = os.getenv("CONFLUENCE_URL")
        self.default_space_key = os.getenv("DEFAULT_SPACE_KEY", "EXAMPLE")

        if not self.confluence_url:
            raise LoaderException("CONFLUENCE_URL environment variable is required")

    def _create_confluence_config(self, source: SourceBean) -> ConfluenceConfig:
        """Crée la configuration Confluence à partir de la source et des secrets"""
        try:
            # Récupérer les credentials depuis le secret manager
            confluence_credentials = self.config_with_secret.get_confluence_credentials(source.perimeter_code)

            config_params = {
                "url": self.confluence_url,
                "default_space_key": self.default_space_key,
                "timeout": int(os.getenv("CONFLUENCE_TIMEOUT", "30"))
            }

            # Ajouter les credentials selon le type disponible
            if confluence_credentials.get("pat_token"):
                config_params["pat_token"] = confluence_credentials["pat_token"]
            elif confluence_credentials.get("api_token") and confluence_credentials.get("username"):
                config_params["username"] = confluence_credentials["username"]
                config_params["api_token"] = confluence_credentials["api_token"]
            else:
                raise LoaderException("No valid Confluence credentials found (PAT token or username/api_token)")

            return ConfluenceConfig(**config_params)

        except Exception as e:
            raise LoaderException(f"Failed to create Confluence configuration: {str(e)}")

    def _create_search_criteria(self, source: SourceBean) -> SearchCriteria:
        """Crée les critères de recherche à partir de la configuration de la source"""
        config = source.parse_configuration()

        # Espaces Confluence à synchroniser
        spaces = config.get("spaces", [self.default_space_key])
        if isinstance(spaces, str):
            spaces = [spaces]

        # Autres critères de recherche
        criteria_params = {
            "spaces": spaces,
            "max_results": config.get("max_results", 1000),
            "include_attachments": config.get("include_attachments", True),
            "content_types": config.get("content_types", ["page", "blogpost"]),
        }

        # Ajout du critère last_modified_days s'il est présent
        last_modified_days = config.get("last_modified_days")
        if last_modified_days is not None:
            try:
                last_modified_days_int = int(last_modified_days)
                if last_modified_days_int > 0:
                    criteria_params["last_modified_days"] = last_modified_days_int
                    self.logger.info(f"Filtrage des documents modifiés depuis {last_modified_days_int} jours")
            except (ValueError, TypeError):
                self.logger.warning(f"Valeur invalide pour last_modified_days: {last_modified_days}, ce critère sera ignoré")

        # Filtres optionnels
        if config.get("labels"):
            criteria_params["labels"] = config["labels"]
        if config.get("title_contains"):
            criteria_params["title_contains"] = config["title_contains"]
        if config.get("exclude_labels"):
            criteria_params["exclude_labels"] = config["exclude_labels"]

        return SearchCriteria(**criteria_params)

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Récupère la liste des documents Confluence"""
        try:
            self.logger.info(f"Getting document list for Confluence source: {source.code}")

            # Créer les configurations
            confluence_config = self._create_confluence_config(source)
            search_criteria = self._create_search_criteria(source)

            # Configuration de stockage temporaire (pour la récupération de la liste)
            storage_config = StorageConfig(
                storage_type="filesystem",
                output_dir="/tmp/confluence_temp"
            )

            # Configuration de traitement - désactiver le change tracking car kbot-load-scheduler
            # gère déjà le suivi des changements via DocumentService.compare_document_list()
            processing_config = ProcessingConfig.from_env()
            processing_config.enable_change_tracking = False

            # Créer l'orchestrateur synchrone
            orchestrator = SyncOrchestrator(
                confluence_config,
                search_criteria,
                storage_config,
                processing_config
            )

            # Exécuter la récupération de manière synchrone (plus besoin d'asyncio)
            # Récupérer seulement la liste des contenus (sans téléchargement)
            content_items = orchestrator.client.search_content(search_criteria)

            # Convertir en DocumentBean
            documents = []
            for item in content_items:
                doc_bean = self._content_item_to_document_bean(source, item)
                documents.append(doc_bean)

                # Ajouter les attachments comme documents séparés
                for attachment in item.attachments:
                    att_bean = self._attachment_to_document_bean(source, item, attachment)
                    documents.append(att_bean)

            self.logger.info(f"Found {len(documents)} documents for source {source.code}")
            return documents

        except Exception as e:
            self.logger.error(f"Error getting document list for source {source.code}: {str(e)}")
            raise LoaderException(f"Failed to get document list: {str(e)}")

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Récupère un document Confluence spécifique"""
        try:
            self.logger.info(f"Getting document: {document.id}")

            # Créer les configurations
            confluence_config = self._create_confluence_config(source)
            search_criteria = self._create_search_criteria(source)

            # Configuration de stockage vers GCS
            storage_config = StorageConfig(
                storage_type="gcs",
                gcs_bucket_name=self._extract_bucket_from_path(output_path),
                gcs_base_prefix=self._extract_prefix_from_path(output_path)
            )

            # Configuration de traitement - désactiver le change tracking car kbot-load-scheduler
            # gère déjà le suivi des changements via DocumentService.compare_document_list()
            processing_config = ProcessingConfig.from_env()
            processing_config.enable_change_tracking = False

            # Créer l'orchestrateur synchrone
            orchestrator = SyncOrchestrator(
                confluence_config,
                search_criteria,
                storage_config,
                processing_config
            )

            # Exécuter la synchronisation complète de manière synchrone
            sync_result = orchestrator.run()

            # Construire les métadonnées
            metadata = {
                Metadata.DOCUMENT_ID: document.id,
                Metadata.DOCUMENT_NAME: document.name,
                Metadata.LOCATION: output_path,
                Metadata.DOMAIN_CODE: source.domain_code,
                Metadata.SOURCE_CODE: source.code,
                Metadata.SOURCE_TYPE: "confluence",
                Metadata.MODIFICATION_TIME: document.modification_time
            }

            # Ajouter des métadonnées spécifiques à Confluence
            if sync_result:
                metadata["confluence_sync_stats"] = sync_result

            return metadata

        except Exception as e:
            self.logger.error(f"Error getting document {document.id}: {str(e)}")
            raise LoaderException(f"Failed to get document: {str(e)}")

    def _content_item_to_document_bean(self, source: SourceBean, item: ContentItem) -> DocumentBean:
        """Convertit un ContentItem en DocumentBean"""
        doc_id = f"{source.domain_code}/{source.code}/page_{item.id}"

        return DocumentBean(
            id=doc_id,
            name=self._sanitize_filename(item.title),
            path=item.web_ui_link or f"{self.confluence_url}/pages/viewpage.action?pageId={item.id}",
            modification_time=item.last_updated
        )

    @staticmethod
    def _sanitize_filename(filename: str) -> str:
        """
        Nettoie un nom de fichier pour qu'il soit compatible avec GCS et les systèmes de fichiers.
        
        - Remplace les caractères spéciaux problématiques par des underscores
        - Évite les noms trop longs
        """
        # Remplacer les caractères spéciaux par des underscores
        # Caractères problématiques pour les systèmes de fichiers et chemins URL
        sanitized = re.sub(r'[\\/:*?"<>|#%&{}+]', '_', filename)
        
        # Limiter la taille (facultatif, ajuster selon les besoins)
        if len(sanitized) > 200:
            sanitized = sanitized[:197] + '...'
            
        # Supprimer les espaces de début et fin
        sanitized = sanitized.strip()
        
        # S'assurer qu'on a au moins un caractère
        if not sanitized:
            sanitized = "unnamed"
            
        return sanitized

    @staticmethod
    def _attachment_to_document_bean(source: SourceBean, parent_item: ContentItem, attachment) -> DocumentBean:
        """Convertit un attachment en DocumentBean"""
        doc_id = f"{source.domain_code}/{source.code}/attachment_{attachment.id}"
        
        # Nettoyer les noms du parent et de la pièce jointe pour éviter les problèmes avec les chemins de fichiers
        sanitized_parent_title = ConfluenceLoader._sanitize_filename(parent_item.title)
        sanitized_attachment_title = ConfluenceLoader._sanitize_filename(attachment.title)
        
        # Conserver le format parent/attachment mais avec des noms nettoyés
        document_name = f"{sanitized_parent_title}/{sanitized_attachment_title}"
    
        return DocumentBean(
            id=doc_id,
            name=document_name,
            path=attachment.download_link,
            modification_time=attachment.created or parent_item.last_updated
        )

    def _extract_bucket_from_path(self, gcs_path: str) -> str:
        """Extrait le nom du bucket d'un chemin GCS"""
        if gcs_path.startswith("gs://"):
            return gcs_path.split("/")[2]
        return gcs_path.split("/")[0]

    def _extract_prefix_from_path(self, gcs_path: str) -> str:
        """Extrait le préfixe d'un chemin GCS"""
        if gcs_path.startswith("gs://"):
            parts = gcs_path.split("/")[3:]
        else:
            parts = gcs_path.split("/")[1:]
        return "/".join(parts) if parts else ""
