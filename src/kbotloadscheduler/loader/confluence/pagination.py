#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Pagination utilities for the Confluence  client.
"""

from typing import List, Tuple
from .constants import APIConstants


class PaginationCalculator:
    """Handles pagination calculations for parallel requests."""

    @staticmethod
    def calculate_pages(
            total_results: int,
            max_results: int,
            page_size: int
    ) -> List[Tuple[int, int]]:
        """Calculate page ranges for parallel fetching."""
        if page_size <= 0:
            page_size = APIConstants.DEFAULT_PAGE_SIZE

        results_to_fetch = total_results
        if 0 < max_results < total_results:
            results_to_fetch = max_results

        pages: List[Tuple[int, int]] = []
        start = 0

        while start < results_to_fetch:
            current_limit = min(page_size, results_to_fetch - start)
            if current_limit <= 0:
                break
            pages.append((start, current_limit))
            start += current_limit

        return pages
