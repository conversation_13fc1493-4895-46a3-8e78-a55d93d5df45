#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gestionnaire centralisé des pools de threads pour optimiser les performances.
"""

import asyncio
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, Callable, Optional, TypeVar, Awaitable
from functools import wraps
import weakref

from .config import ThreadPoolConfig

T = TypeVar('T')


class ThreadPoolManager:
    """
    Gestionnaire centralisé des pools de threads pour optimiser les performances.
    
    Cette classe gère plusieurs pools de threads spécialisés :
    - Pool I/O : pour les opérations de lecture/écriture de fichiers
    - Pool de traitement de documents : pour l'extraction de texte
    - Pool API : pour les appels API synchrones
    """
    
    _instance: Optional['ThreadPoolManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls, config: ThreadPoolConfig = None):
        """Implémentation du pattern Singleton thread-safe."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: ThreadPoolConfig = None):
        """Initialise le gestionnaire de pools de threads."""
        # Éviter la réinitialisation si l'instance existe déjà
        if hasattr(self, '_initialized'):
            return
            
        self.config = config or ThreadPoolConfig()
        self.logger = logging.getLogger(__name__)
        
        # Pools de threads spécialisés
        self._pools: Dict[str, ThreadPoolExecutor] = {}
        self._shutdown = False
        
        # Créer les pools de threads
        self._create_thread_pools()
        
        # Enregistrer le nettoyage automatique
        weakref.finalize(self, self._cleanup_pools, self._pools)
        
        self._initialized = True
        
        self.logger.info(
            f"ThreadPoolManager initialisé avec "
            f"io_workers={self.config.io_thread_workers}, "
            f"document_workers={self.config.document_processing_workers}, "
            f"api_workers={self.config.api_thread_workers}"
        )
    
    def _create_thread_pools(self):
        """Crée les pools de threads spécialisés."""
        # Pool pour les opérations I/O (lecture/écriture de fichiers)
        self._pools['io'] = ThreadPoolExecutor(
            max_workers=self.config.io_thread_workers,
            thread_name_prefix=f"{self.config.thread_name_prefix}-IO"
        )
        
        # Pool pour le traitement de documents (extraction de texte)
        self._pools['document'] = ThreadPoolExecutor(
            max_workers=self.config.document_processing_workers,
            thread_name_prefix=f"{self.config.thread_name_prefix}-Doc"
        )
        
        # Pool pour les appels API synchrones
        self._pools['api'] = ThreadPoolExecutor(
            max_workers=self.config.api_thread_workers,
            thread_name_prefix=f"{self.config.thread_name_prefix}-API"
        )
        
        self.logger.debug("Pools de threads créés avec succès")
    
    def get_pool(self, pool_type: str) -> ThreadPoolExecutor:
        """
        Récupère un pool de threads par son type.
        
        Args:
            pool_type: Type de pool ('io', 'document', 'api')
            
        Returns:
            Le pool de threads correspondant
            
        Raises:
            ValueError: Si le type de pool n'existe pas
        """
        if self._shutdown:
            raise RuntimeError("ThreadPoolManager a été fermé")
            
        if pool_type not in self._pools:
            raise ValueError(f"Type de pool inconnu: {pool_type}. Types disponibles: {list(self._pools.keys())}")
            
        return self._pools[pool_type]
    
    async def run_in_io_pool(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Exécute une fonction dans le pool I/O.
        
        Args:
            func: Fonction à exécuter
            *args: Arguments positionnels
            **kwargs: Arguments nommés
            
        Returns:
            Le résultat de la fonction
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.get_pool('io'), func, *args, **kwargs)
    
    async def run_in_document_pool(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Exécute une fonction dans le pool de traitement de documents.
        
        Args:
            func: Fonction à exécuter
            *args: Arguments positionnels
            **kwargs: Arguments nommés
            
        Returns:
            Le résultat de la fonction
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.get_pool('document'), func, *args, **kwargs)
    
    async def run_in_api_pool(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Exécute une fonction dans le pool API.
        
        Args:
            func: Fonction à exécuter
            *args: Arguments positionnels
            **kwargs: Arguments nommés
            
        Returns:
            Le résultat de la fonction
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.get_pool('api'), func, *args, **kwargs)
    
    def submit_to_pool(self, pool_type: str, func: Callable[..., T], *args, **kwargs) -> Future[T]:
        """
        Soumet une tâche à un pool spécifique et retourne un Future.

        Args:
            pool_type: Type de pool ('io', 'document', 'api')
            func: Fonction à exécuter
            *args: Arguments positionnels
            **kwargs: Arguments nommés

        Returns:
            Future représentant l'exécution de la tâche
        """
        return self.get_pool(pool_type).submit(func, *args, **kwargs)

    def run_in_pool_sync(self, pool_type: str, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Exécute une fonction dans un pool spécifique de manière synchrone.

        Args:
            pool_type: Type de pool ('io', 'document', 'api')
            func: Fonction à exécuter
            *args: Arguments positionnels
            **kwargs: Arguments nommés

        Returns:
            Le résultat de la fonction
        """
        future = self.submit_to_pool(pool_type, func, *args, **kwargs)
        return future.result()

    def run_in_io_pool_sync(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Exécute une fonction dans le pool I/O de manière synchrone."""
        return self.run_in_pool_sync('io', func, *args, **kwargs)

    def run_in_document_pool_sync(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Exécute une fonction dans le pool de traitement de documents de manière synchrone."""
        return self.run_in_pool_sync('document', func, *args, **kwargs)

    def run_in_api_pool_sync(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Exécute une fonction dans le pool API de manière synchrone."""
        return self.run_in_pool_sync('api', func, *args, **kwargs)
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Récupère les statistiques des pools de threads.
        
        Returns:
            Dictionnaire contenant les statistiques de chaque pool
        """
        stats = {}
        for pool_name, pool in self._pools.items():
            stats[pool_name] = {
                'max_workers': pool._max_workers,
                'active_threads': len([t for t in pool._threads if t.is_alive()]),
                'queue_size': pool._work_queue.qsize() if hasattr(pool._work_queue, 'qsize') else 'N/A'
            }
        return stats
    
    def shutdown(self, wait: bool = True):
        """
        Ferme tous les pools de threads.
        
        Args:
            wait: Si True, attend que toutes les tâches en cours se terminent
        """
        if self._shutdown:
            return
            
        self.logger.info("Fermeture des pools de threads...")
        self._shutdown = True
        
        for pool_name, pool in self._pools.items():
            try:
                pool.shutdown(wait=wait)
                self.logger.debug(f"Pool {pool_name} fermé")
            except Exception as e:
                self.logger.error(f"Erreur lors de la fermeture du pool {pool_name}: {e}")
        
        self._pools.clear()
        self.logger.info("Tous les pools de threads ont été fermés")
    
    @staticmethod
    def _cleanup_pools(pools: Dict[str, ThreadPoolExecutor]):
        """Méthode de nettoyage appelée automatiquement."""
        for pool in pools.values():
            try:
                pool.shutdown(wait=False)
            except Exception:
                pass  # Ignorer les erreurs lors du nettoyage
    
    def __enter__(self):
        """Support pour l'utilisation avec 'with'."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Ferme automatiquement les pools lors de la sortie du contexte."""
        self.shutdown()


def thread_pool_executor(pool_type: str = 'io'):
    """
    Décorateur pour exécuter automatiquement une fonction dans un pool de threads.
    
    Args:
        pool_type: Type de pool à utiliser ('io', 'document', 'api')
        
    Returns:
        Décorateur qui transforme une fonction synchrone en fonction asynchrone
    """
    def decorator(func: Callable[..., T]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            manager = ThreadPoolManager()
            
            if pool_type == 'io':
                return await manager.run_in_io_pool(func, *args, **kwargs)
            elif pool_type == 'document':
                return await manager.run_in_document_pool(func, *args, **kwargs)
            elif pool_type == 'api':
                return await manager.run_in_api_pool(func, *args, **kwargs)
            else:
                raise ValueError(f"Type de pool inconnu: {pool_type}")
        
        return wrapper
    return decorator


# Instance globale pour faciliter l'utilisation
_global_manager: Optional[ThreadPoolManager] = None


def get_thread_pool_manager(config: ThreadPoolConfig = None) -> ThreadPoolManager:
    """
    Récupère l'instance globale du gestionnaire de pools de threads.
    
    Args:
        config: Configuration optionnelle (utilisée seulement lors de la première création)
        
    Returns:
        Instance du ThreadPoolManager
    """
    global _global_manager
    if _global_manager is None:
        _global_manager = ThreadPoolManager(config)
    return _global_manager


def shutdown_global_thread_pools():
    """Ferme l'instance globale des pools de threads."""
    global _global_manager
    if _global_manager is not None:
        _global_manager.shutdown()
        _global_manager = None
