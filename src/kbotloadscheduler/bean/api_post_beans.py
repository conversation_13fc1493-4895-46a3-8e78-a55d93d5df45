from pydantic import BaseModel


class LoaderGetListFileBean(BaseModel):
    get_list_file: str


class LoaderDocumentListFileBean(BaseModel):
    document_list_file: str


class RepoDocumentListFileBean(BaseModel):
    repo_document_list_file: str


class EmbeddDocumentFileBean(BaseModel):
    embedd_document_metadata_file: str


class RemoveDocumentFileBean(BaseModel):
    remove_document_file: str


class GetDocumentFileBean(BaseModel):
    document_get_file: str
