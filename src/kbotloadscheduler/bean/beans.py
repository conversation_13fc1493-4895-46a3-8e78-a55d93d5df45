import re

from pydantic import BaseModel, Field
import json
from datetime import datetime
from typing import ClassVar, Annotated


FORMAT_DATETIME: str = '%Y%m%d%H%M%S'
LOAD_DATE_FORMAT: str = '%Y%m%d%H%M'
FORMAT_MODIFICATION_TIME_EMBEDDING_API: str = "%Y-%m-%dT%H:%M:%S"


class TreatmentBean(BaseModel):
    """Représentation d'un appel url pour realiser un traitement dans la chaine de récupération des documents"""
    file_type: Annotated[str, Field(description="Type de traitement : getlist, list, getdoc, removedoc, docs")]
    url: Annotated[str, Field(description="Url relative à appeler : contient l'action à réaliser et le périmètre")]
    params: Annotated[dict, Field(description="Paramètres à passer à l'url : contient le fichier à traiter")]


class SourceBean(BaseModel):
    """Représentation d'une source de documents"""
    id: Annotated[int, Field(description="Identifiant de la source")]
    code: Annotated[str, Field(description="Code de la source : utilisé comme répertoire de travail")]
    label: Annotated[str, Field(description="Label de la source")]
    src_type: Annotated[str, Field(description="Type de la source : associé au type de loader à utiliser")]
    configuration: Annotated[str, Field(description="Configuration json de la source pour le loader")]
    last_load_time: Annotated[int, Field(
        description="Timestamp de dernier chargement des documents pour cette source")]
    load_interval: Annotated[int, Field(description="Délai en heure entre deux chargements pour cette source")]
    domain_code: Annotated[str, Field(description="Code du domaine auquel la source est rattaché")]
    perimeter_code: Annotated[str, Field(description="Code du périmètre : relié à la base vecteur à utiliser")]
    force_embedding: Annotated[
        bool, Field(description="Permet de forcer recuperation et embedding de tous les docs de la source")] = False

    def parse_configuration(self) -> dict:
        """Récupère la configuration sous la forme d'un dictionanire"""
        return json.loads(self.configuration)

    def get_expected_conf(self, conf_name: str):
        """Récupère un champ précis de la configuration"""
        return self.parse_configuration().get(conf_name, '')


class DocumentBean(BaseModel):
    """Représentation d'un document permettant de lier un document de la source avec le document de la base vecteur"""
    id: Annotated[str, Field(description="Identifiant externe du document")]
    path: Annotated[str, Field(description="Chemin relatif du document")]
    name: Annotated[str, Field(description="Nom ou chemin du document")]
    modification_time: Annotated[datetime, Field(description="Date et heure de dernière modification du document")]

    def to_serializable_dict(self) -> dict:
        """Serialize le document pour écriture dans les fichiers"""
        modif_time = self.modification_time
        serializable_dict = {**self.__dict__, 'modification_time': modif_time.strftime(FORMAT_DATETIME)}
        return serializable_dict

    @classmethod
    def from_serializable_dict(cls, document: dict):
        """Crée le document à partir de sa version sérialisée lue dans un fichier"""
        modif_str_time = document.get('modification_time')
        return DocumentBean(
            id=document.get('id', ''),
            name=document.get('name', ''),
            path=document.get('path', ''),
            modification_time=datetime.strptime(modif_str_time, FORMAT_DATETIME)
        )

    @classmethod
    def from_embedding_api(cls, document: dict):
        """créé le document à partir des informations récupérées de l'api embedding"""
        return DocumentBean(
            id=document.get('document_id', ''),
            name='',
            path='',
            modification_time=datetime.strptime(document.get('modification_date', ''),
                                                FORMAT_MODIFICATION_TIME_EMBEDDING_API)
        )


class DocumentAnswerBean(BaseModel):
    """Format d'un document dans la réponse des api"""
    id: Annotated[str, Field(description="Identifiant externe du document")]
    path: Annotated[str, Field(description="Chemin relatif du document")]
    name: Annotated[str, Field(description="Nom ou chemin du document")]
    modification_time: Annotated[str, Field(description="Date et heure de dernière modification du document")]


class Metadata:
    """Champs génériques que l'on trouve dans les metadata d'un document"""
    DATETIME_REGEXP = r'^\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])([01][0-9]|2[0-3])[0-5][0-9][0-5][0-9]$'

    DOCUMENT_ID: ClassVar[str] = 'document_id'
    DOCUMENT_NAME: ClassVar[str] = 'document_name'
    SOURCE_URL: ClassVar[str] = 'source_path'
    LOCATION: ClassVar[str] = 'location'
    CREATION_TIME: ClassVar[str] = 'creationDate'
    MODIFICATION_TIME: ClassVar[str] = 'modificationDate'
    DOMAIN_CODE: ClassVar[str] = 'domain'
    SOURCE_CODE: ClassVar[str] = 'source'
    SOURCE_TYPE: ClassVar[str] = 'source_type'
    SOURCE_CONF: ClassVar[str] = 'source_conf'

    @classmethod
    def match_date_value(cls, value: str) -> bool:
        """Vérifie si une valeur est au format date"""
        return isinstance(value, str) and re.search(cls.DATETIME_REGEXP, value) is not None

    @classmethod
    def serialize(cls, md: dict) -> dict:
        """Serialize les metadata pour les écrire dans un fichier"""
        serialized = {}
        for key in md:
            val = md.get(key)
            if isinstance(val, datetime):
                val = val.strftime(FORMAT_DATETIME)
            serialized[key] = val
        return serialized

    @classmethod
    def unserialize(cls, md: dict) -> dict:
        """Récupère les metadata à partir de leur version sérialisée lue dans un fichier"""
        unserialized = {}
        for key in md:
            val = md.get(key)
            if cls.match_date_value(val):
                val = datetime.strptime(val, FORMAT_DATETIME)
            unserialized[key] = val
        return unserialized


class DocumentWithMetadataBean(BaseModel):
    """Représentation d'un document avec ses metadatas"""
    document: Annotated[DocumentAnswerBean, Field(description="Le document")]
    metadata: Annotated[dict, Field(description="Les metadata du documents")]


class ResultBean(BaseModel):
    """Résultat d'un appel d'api"""
    status: Annotated[str, Field(description="Status du traitement : ok ou ko")]
    error: Annotated[str, Field(description="Message d'erreur si le status est ko")]
