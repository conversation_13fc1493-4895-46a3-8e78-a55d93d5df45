from typing import List, Union

from ..apicall.kbot_embedding_api import KbotEmbeddingApi
from ..bean.beans import DocumentBean, ResultBean
from ..gcs.treatment_file_manager import TreatmentFileManager
from .document_list_comparator import DocumentListComparator


class DocumentService:
    """Service permettant de vérifier quels document du repo sont déjà embeddés
    et d'embedder ou de supprimer les documents dans la base de vecteur

    :field kbot_embedding_api: classe d'appel de l'api kbot-embedding
    :field treatment_file_manager: classe permettant la lecture
        et l'écriture des fichiers list, getdoc, doc, removedoc
    """

    def __init__(self,
                 kbot_embedding_api: KbotEmbeddingApi,
                 treatment_file_manager: TreatmentFileManager) -> None:
        """Constructeur

        :param kbot_embedding_api: classe d'appel de l'api kbot-embedding
        :param treatment_file_manager: classe permettant la lecture
            et l'écriture des fichiers list, getdoc, doc, removedoc
        """
        self._kbot_embedding_api: KbotEmbeddingApi = kbot_embedding_api
        self._treatment_file_manager: TreatmentFileManager = treatment_file_manager

    def compare_document_list(self, perimeter_code: str, document_list_file: str) -> List[DocumentBean]:
        """Comparaison de la liste des documents du repo avec la liste des documents embeddés

        :param perimeter_code: code du périmètre. Permet de savoir où écrire les fichiers getdoc et removedoc
        :param document_list_file: fichier contenant la configuration de la source et la liste des documents du repo
        :return: la liste des documents à récupérer
        """
        try:
            self._treatment_file_manager.set_in_progress(document_list_file)
            [load_date, domain_code, source_code, file_type, file_sig] = \
                self._treatment_file_manager.extract_info_from_file_name(document_list_file)
            document_list = self._treatment_file_manager.read_document_list(document_list_file)
            source = document_list.get('source')
            repo_documents = document_list.get('documents')
            embedded_documents = self._kbot_embedding_api.get_document_list(perimeter_code, domain_code, source_code)
            comparator = DocumentListComparator()
            comparator.compare(embedded_documents, repo_documents, source.force_embedding)

            for document in comparator.get_documents_to_get():
                self._treatment_file_manager.write_document_get_file(load_date, source, document)

            self._treatment_file_manager.write_document_list(
                load_date, source, comparator.get_documents_to_remove(), TreatmentFileManager.REMOVE_DOC)
            self._treatment_file_manager.set_done(document_list_file)
            return sorted([document.to_serializable_dict() for document in comparator.get_documents_to_get()],
                          key=lambda d: d['id'])
        finally:
            self._treatment_file_manager.unset_in_progress(document_list_file)

    def embedd_document(self, perimeter_code: str, embedd_document_metadata_file: str) -> Union[DocumentBean, ResultBean]:
        """Appel de l'api d'embedding

        :param perimeter_code: code du périmètre
        :param embedd_document_metadata_file: metadata du fichier à embedder
        :return: le document embeddé
        """
        metadata_json = ""
        try:
            self._treatment_file_manager.set_in_progress(embedd_document_metadata_file)
            metadata_json = self._treatment_file_manager.read_document_metadata(embedd_document_metadata_file)
            result = self._kbot_embedding_api.embedd_document(perimeter_code, embedd_document_metadata_file)
            if result['status'] == 'ok':
                self._treatment_file_manager.move_embedded_document_to_done(perimeter_code, metadata_json)
                result['document'] = result['document'].to_serializable_dict()
                self._treatment_file_manager.set_done(embedd_document_metadata_file)
                return result
        except KbotEmbeddingApi.EmbeddingException as e:
                self._treatment_file_manager.move_embedded_document_to_error(
                    perimeter_code, metadata_json, str(e))
                self._treatment_file_manager.set_done(embedd_document_metadata_file)
                raise e
        finally:
            self._treatment_file_manager.unset_in_progress(embedd_document_metadata_file)

    def remove_documents(self, perimeter_code: str, document_list_file: str) -> List[DocumentBean]:
        """Appel de l'api de suppression du fichier

        :param perimeter_code: code du périmètre
        :param document_list_file: fichier à supprimer
        :return: le document supprimé
        """
        try:
            self._treatment_file_manager.set_in_progress(document_list_file)
            document_list = self._treatment_file_manager.read_document_list(document_list_file)
            remove_documents = document_list.get('documents')
            return_documents = []
            for document in remove_documents:
                if self._kbot_embedding_api.remove_document(perimeter_code, document):
                    return_documents.append(document.to_serializable_dict())
            self._treatment_file_manager.set_done(document_list_file)
            return return_documents
        finally:
            self._treatment_file_manager.unset_in_progress(document_list_file)
