from typing import List

from ..loader.abstract_loader import LoaderException
from ..loader.loader_manager import LoaderManager
from ..gcs.treatment_file_manager import TreatmentFileManager
from ..bean.beans import DocumentBean, Metadata


class LoaderService:
    """Classe d'appel des loader pour récupérer les listes de documents dans le repo
        et pour récupérer les documents du repo

    :field loader_manager: classe permettant de choisir le loader en fonction du type de source
    :field treatment_file_manager: classe permettant de lire et écrire les ficheirs get-list, list, get-doc
    """

    def __init__(self,
                 loader_manager: LoaderManager,
                 treatment_file_manager: TreatmentFileManager) -> None:
        """Constructeur

        :param loader_manager: classe permettant de choisir le loader en fonction du type de source
        :param treatment_file_manager: classe permettant de lire et écrire les ficheirs get-list, list, get-doc
        """
        self._loader_manager: LoaderManager = loader_manager
        self._treatment_file_manager: TreatmentFileManager = treatment_file_manager

    def get_document_list(self, perimeter_code: str, get_list_file: str) -> List[dict]:
        """Appel du loader pour récupérer la liste des documents du repo pour une source

        :param perimeter_code: code du périmètre. Permet de savoir où écrire la liste des documents
        :param get_list_file: fichier contenant la configuration de la source
        pour laquelle récupérer la liste des documents
        :return: liste des documents de la source
        """
        try:
            self._treatment_file_manager.set_in_progress(get_list_file)
            [load_date, domain_code, source_code, file_type, file_sig] = \
                self._treatment_file_manager.extract_info_from_file_name(get_list_file)
            source = self._treatment_file_manager.read_get_list(get_list_file)
            loader = self._loader_manager.get_loader(source.src_type)
            documents = loader.get_document_list(source)
            serialized_documents = self._treatment_file_manager.write_document_list(load_date, source, documents)
            self._treatment_file_manager.set_done(get_list_file)
            return serialized_documents
        finally:
            self._treatment_file_manager.unset_in_progress(get_list_file)

    def get_document(self, perimeter_code: str, document_get_file: str) -> dict:
        """Appel du loader pour récupérer un document

        :param perimeter_code: code du périmètre. Permet de savoir où placer le documents récupérés
        :param document_get_file: fichier contenant la configuration de la source du document à récupérer
        :return: les informations du document a recuperer
        """
        try:
            self._treatment_file_manager.set_in_progress(document_get_file)
            [load_date, domain_code, source_code, file_type, file_sig] = \
                self._treatment_file_manager.extract_info_from_file_name(document_get_file)
            document_infos = self._treatment_file_manager.read_document_get_file(document_get_file)
            source = document_infos.get('source')
            document_to_get: DocumentBean = document_infos.get('document')
            base_metadata = {
                Metadata.DOMAIN_CODE: domain_code,
                Metadata.SOURCE_CODE: source_code,
                Metadata.SOURCE_TYPE: source.src_type,
                Metadata.SOURCE_CONF: source.configuration
            }
            loader = self._loader_manager.get_loader(source.src_type)
            output_path = self._treatment_file_manager.build_directory_name(
                perimeter_code, load_date, source.domain_code, source.code, TreatmentFileManager.DOCS)
            document_metadata = loader.get_document(source, document_to_get, output_path)
            md = {**base_metadata, **document_metadata}
            serialized_md = self._treatment_file_manager.write_document_metadata(md)
            self._treatment_file_manager.set_done(document_get_file)
            return {'document': document_to_get.to_serializable_dict(), 'metadata': serialized_md}
        except LoaderException as e:
            self._treatment_file_manager.set_in_error(document_get_file, str(e))
            raise LoaderException(str(e))
        finally:
            self._treatment_file_manager.unset_in_progress(document_get_file)
